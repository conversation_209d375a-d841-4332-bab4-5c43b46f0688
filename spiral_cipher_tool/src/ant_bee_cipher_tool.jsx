// Full Ant-Bee-Spider Cipher Tool with Visuals and Animations
// Includes animated encryption and NEW animated decryption spiral step
import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";

function SpiralGridDecryption({ message }) {
  const gridSize = 5;
  const [grid, setGrid] = useState(Array(25).fill(""));

  useEffect(() => {
    const cleaned = message.toUpperCase().replace(/[^A-Z]/g, "").padEnd(25, "X").slice(0, 25);
    let tempGrid = Array(25).fill("");

    let dx = [0, 1, 0, -1];
    let dy = [1, 0, -1, 0];
    let x = 0,
      y = 0,
      dir = 0,
      idx = 0;

    let coords = [];
    for (let i = 0; i < 25; i++) {
      coords.push([x, y]);
      let nx = x + dx[dir];
      let ny = y + dy[dir];
      if (
        nx < 0 ||
        ny < 0 ||
        nx >= gridSize ||
        ny >= gridSize ||
        coords.some(([cx, cy]) => cx === nx && cy === ny)
      ) {
        dir = (dir + 1) % 4;
        nx = x + dx[dir];
        ny = y + dy[dir];
      }
      x = nx;
      y = ny;
    }

    coords.forEach(([cx, cy], i) => {
      tempGrid[cx * gridSize + cy] = cleaned[i];
    });

    setGrid(tempGrid);
  }, [message]);

  return (
    <div style={{
      display: 'grid',
      gridTemplateColumns: 'repeat(5, 1fr)',
      gap: '4px',
      marginTop: '16px',
      maxWidth: '300px'
    }}>
      {grid.map((value, idx) => (
        <motion.div
          key={idx}
          style={{
            width: '40px',
            height: '40px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            border: '1px solid #93c5fd',
            borderRadius: '4px',
            backgroundColor: '#dbeafe',
            color: '#1e40af',
            fontWeight: 'bold'
          }}
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: idx * 0.08 }}
        >
          {value}
        </motion.div>
      ))}
    </div>
  );
}

function SpiralCipherVisualizer() {
  const [message, setMessage] = useState("MEETATNOON");
  const [mode, setMode] = useState("encrypt");

  return (
    <div style={{
      backgroundColor: '#fefce8',
      border: '1px solid #facc15',
      padding: '16px',
      borderRadius: '12px',
      marginTop: '24px'
    }}>
      <h2 style={{
        fontSize: '24px',
        fontWeight: 'bold',
        color: '#92400e',
        marginBottom: '16px'
      }}>🔄 Spiral Cipher Animator</h2>
      <div style={{ marginBottom: '16px' }}>
        <input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Enter your message"
          style={{
            width: '100%',
            padding: '8px 12px',
            border: '1px solid #ccc',
            borderRadius: '4px',
            fontSize: '16px'
          }}
        />
      </div>
      <div style={{ display: 'flex', gap: '8px', marginBottom: '16px' }}>
        <button
          onClick={() => setMode("encrypt")}
          style={{
            padding: '8px 16px',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '14px',
            backgroundColor: mode === "encrypt" ? "#d97706" : "#fef3c7",
            color: mode === "encrypt" ? "white" : "#92400e"
          }}
        >
          🕷️ Encrypt
        </button>
        <button
          onClick={() => setMode("decrypt")}
          style={{
            padding: '8px 16px',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '14px',
            backgroundColor: mode === "decrypt" ? "#2563eb" : "#dbeafe",
            color: mode === "decrypt" ? "white" : "#1e40af"
          }}
        >
          🔓 Decrypt
        </button>
      </div>
      {mode === "encrypt" ? (
        <SpiralGridAnimationWithLetters message={message} />
      ) : (
        <SpiralGridDecryption message={message} />
      )}
    </div>
  );
}

function SpiralGridAnimationWithLetters({ message }) {
  const gridSize = 5;
  const [grid, setGrid] = useState(Array.from({ length: 25 }, () => ""));

  useEffect(() => {
    const cleaned = message.toUpperCase().replace(/[^A-Z]/g, "").padEnd(25, "X").slice(0, 25);
    let spiralGrid = Array.from({ length: gridSize }, () => Array(gridSize).fill(""));
    let dx = [0, 1, 0, -1];
    let dy = [1, 0, -1, 0];
    let dir = 0;
    let x = 0,
      y = 0,
      idx = 0;
    for (let i = 0; i < 25; i++) {
      spiralGrid[x][y] = cleaned[idx++];
      let nx = x + dx[dir];
      let ny = y + dy[dir];
      if (
        nx < 0 ||
        ny < 0 ||
        nx >= gridSize ||
        ny >= gridSize ||
        spiralGrid[nx][ny] !== ""
      ) {
        dir = (dir + 1) % 4;
        nx = x + dx[dir];
        ny = y + dy[dir];
      }
      x = nx;
      y = ny;
    }
    setGrid(spiralGrid.flat());
  }, [message]);

  return (
    <div style={{
      display: 'grid',
      gridTemplateColumns: 'repeat(5, 1fr)',
      gap: '4px',
      marginTop: '16px',
      maxWidth: '300px'
    }}>
      {grid.map((value, idx) => (
        <motion.div
          key={idx}
          style={{
            width: '40px',
            height: '40px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            border: '1px solid #fbbf24',
            borderRadius: '4px',
            backgroundColor: '#fef3c7',
            color: '#92400e',
            fontWeight: 'bold'
          }}
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: idx * 0.08 }}
        >
          {value}
        </motion.div>
      ))}
    </div>
  );
}

export default function AntBeeSpiderTool() {
  return (
    <div style={{ padding: '16px' }}>
      <SpiralCipherVisualizer />
    </div>
  );
}
