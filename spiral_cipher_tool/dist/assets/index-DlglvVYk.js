(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();function Uh(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var lf={exports:{}},yo={},af={exports:{}},F={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qr=Symbol.for("react.element"),$h=Symbol.for("react.portal"),Wh=Symbol.for("react.fragment"),Hh=Symbol.for("react.strict_mode"),Gh=Symbol.for("react.profiler"),Kh=Symbol.for("react.provider"),Qh=Symbol.for("react.context"),Xh=Symbol.for("react.forward_ref"),Yh=Symbol.for("react.suspense"),Zh=Symbol.for("react.memo"),Jh=Symbol.for("react.lazy"),Xa=Symbol.iterator;function qh(e){return e===null||typeof e!="object"?null:(e=Xa&&e[Xa]||e["@@iterator"],typeof e=="function"?e:null)}var uf={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},cf=Object.assign,ff={};function Zn(e,t,n){this.props=e,this.context=t,this.refs=ff,this.updater=n||uf}Zn.prototype.isReactComponent={};Zn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Zn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function df(){}df.prototype=Zn.prototype;function Rl(e,t,n){this.props=e,this.context=t,this.refs=ff,this.updater=n||uf}var _l=Rl.prototype=new df;_l.constructor=Rl;cf(_l,Zn.prototype);_l.isPureReactComponent=!0;var Ya=Array.isArray,pf=Object.prototype.hasOwnProperty,Nl={current:null},hf={key:!0,ref:!0,__self:!0,__source:!0};function mf(e,t,n){var r,i={},o=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(o=""+t.key),t)pf.call(t,r)&&!hf.hasOwnProperty(r)&&(i[r]=t[r]);var l=arguments.length-2;if(l===1)i.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];i.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)i[r]===void 0&&(i[r]=l[r]);return{$$typeof:Qr,type:e,key:o,ref:s,props:i,_owner:Nl.current}}function bh(e,t){return{$$typeof:Qr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Fl(e){return typeof e=="object"&&e!==null&&e.$$typeof===Qr}function em(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Za=/\/+/g;function Uo(e,t){return typeof e=="object"&&e!==null&&e.key!=null?em(""+e.key):t.toString(36)}function Ci(e,t,n,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(o){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case Qr:case $h:s=!0}}if(s)return s=e,i=i(s),e=r===""?"."+Uo(s,0):r,Ya(i)?(n="",e!=null&&(n=e.replace(Za,"$&/")+"/"),Ci(i,t,n,"",function(u){return u})):i!=null&&(Fl(i)&&(i=bh(i,n+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(Za,"$&/")+"/")+e)),t.push(i)),1;if(s=0,r=r===""?".":r+":",Ya(e))for(var l=0;l<e.length;l++){o=e[l];var a=r+Uo(o,l);s+=Ci(o,t,n,a,i)}else if(a=qh(e),typeof a=="function")for(e=a.call(e),l=0;!(o=e.next()).done;)o=o.value,a=r+Uo(o,l++),s+=Ci(o,t,n,a,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function ri(e,t,n){if(e==null)return e;var r=[],i=0;return Ci(e,r,"","",function(o){return t.call(n,o,i++)}),r}function tm(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Se={current:null},Ti={transition:null},nm={ReactCurrentDispatcher:Se,ReactCurrentBatchConfig:Ti,ReactCurrentOwner:Nl};function gf(){throw Error("act(...) is not supported in production builds of React.")}F.Children={map:ri,forEach:function(e,t,n){ri(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return ri(e,function(){t++}),t},toArray:function(e){return ri(e,function(t){return t})||[]},only:function(e){if(!Fl(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};F.Component=Zn;F.Fragment=Wh;F.Profiler=Gh;F.PureComponent=Rl;F.StrictMode=Hh;F.Suspense=Yh;F.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=nm;F.act=gf;F.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=cf({},e.props),i=e.key,o=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,s=Nl.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)pf.call(t,a)&&!hf.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:Qr,type:e.type,key:i,ref:o,props:r,_owner:s}};F.createContext=function(e){return e={$$typeof:Qh,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Kh,_context:e},e.Consumer=e};F.createElement=mf;F.createFactory=function(e){var t=mf.bind(null,e);return t.type=e,t};F.createRef=function(){return{current:null}};F.forwardRef=function(e){return{$$typeof:Xh,render:e}};F.isValidElement=Fl;F.lazy=function(e){return{$$typeof:Jh,_payload:{_status:-1,_result:e},_init:tm}};F.memo=function(e,t){return{$$typeof:Zh,type:e,compare:t===void 0?null:t}};F.startTransition=function(e){var t=Ti.transition;Ti.transition={};try{e()}finally{Ti.transition=t}};F.unstable_act=gf;F.useCallback=function(e,t){return Se.current.useCallback(e,t)};F.useContext=function(e){return Se.current.useContext(e)};F.useDebugValue=function(){};F.useDeferredValue=function(e){return Se.current.useDeferredValue(e)};F.useEffect=function(e,t){return Se.current.useEffect(e,t)};F.useId=function(){return Se.current.useId()};F.useImperativeHandle=function(e,t,n){return Se.current.useImperativeHandle(e,t,n)};F.useInsertionEffect=function(e,t){return Se.current.useInsertionEffect(e,t)};F.useLayoutEffect=function(e,t){return Se.current.useLayoutEffect(e,t)};F.useMemo=function(e,t){return Se.current.useMemo(e,t)};F.useReducer=function(e,t,n){return Se.current.useReducer(e,t,n)};F.useRef=function(e){return Se.current.useRef(e)};F.useState=function(e){return Se.current.useState(e)};F.useSyncExternalStore=function(e,t,n){return Se.current.useSyncExternalStore(e,t,n)};F.useTransition=function(){return Se.current.useTransition()};F.version="18.3.1";af.exports=F;var D=af.exports;const jl=Uh(D);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var rm=D,im=Symbol.for("react.element"),om=Symbol.for("react.fragment"),sm=Object.prototype.hasOwnProperty,lm=rm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,am={key:!0,ref:!0,__self:!0,__source:!0};function yf(e,t,n){var r,i={},o=null,s=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)sm.call(t,r)&&!am.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:im,type:e,key:o,ref:s,props:i,_owner:lm.current}}yo.Fragment=om;yo.jsx=yf;yo.jsxs=yf;lf.exports=yo;var O=lf.exports,Ts={},vf={exports:{}},Ne={},xf={exports:{}},Sf={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(E,A){var N=E.length;E.push(A);e:for(;0<N;){var R=N-1>>>1,W=E[R];if(0<i(W,A))E[R]=A,E[N]=W,N=R;else break e}}function n(E){return E.length===0?null:E[0]}function r(E){if(E.length===0)return null;var A=E[0],N=E.pop();if(N!==A){E[0]=N;e:for(var R=0,W=E.length,Kt=W>>>1;R<Kt;){var qe=2*(R+1)-1,yn=E[qe],Le=qe+1,Qt=E[Le];if(0>i(yn,N))Le<W&&0>i(Qt,yn)?(E[R]=Qt,E[Le]=N,R=Le):(E[R]=yn,E[qe]=N,R=qe);else if(Le<W&&0>i(Qt,N))E[R]=Qt,E[Le]=N,R=Le;else break e}}return A}function i(E,A){var N=E.sortIndex-A.sortIndex;return N!==0?N:E.id-A.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var s=Date,l=s.now();e.unstable_now=function(){return s.now()-l}}var a=[],u=[],c=1,f=null,d=3,g=!1,y=!1,v=!1,k=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function h(E){for(var A=n(u);A!==null;){if(A.callback===null)r(u);else if(A.startTime<=E)r(u),A.sortIndex=A.expirationTime,t(a,A);else break;A=n(u)}}function x(E){if(v=!1,h(E),!y)if(n(a)!==null)y=!0,Z(S);else{var A=n(u);A!==null&&je(x,A.startTime-E)}}function S(E,A){y=!1,v&&(v=!1,m(P),P=-1),g=!0;var N=d;try{for(h(A),f=n(a);f!==null&&(!(f.expirationTime>A)||E&&!re());){var R=f.callback;if(typeof R=="function"){f.callback=null,d=f.priorityLevel;var W=R(f.expirationTime<=A);A=e.unstable_now(),typeof W=="function"?f.callback=W:f===n(a)&&r(a),h(A)}else r(a);f=n(a)}if(f!==null)var Kt=!0;else{var qe=n(u);qe!==null&&je(x,qe.startTime-A),Kt=!1}return Kt}finally{f=null,d=N,g=!1}}var T=!1,C=null,P=-1,_=5,M=-1;function re(){return!(e.unstable_now()-M<_)}function le(){if(C!==null){var E=e.unstable_now();M=E;var A=!0;try{A=C(!0,E)}finally{A?ge():(T=!1,C=null)}}else T=!1}var ge;if(typeof p=="function")ge=function(){p(le)};else if(typeof MessageChannel<"u"){var ie=new MessageChannel,xt=ie.port2;ie.port1.onmessage=le,ge=function(){xt.postMessage(null)}}else ge=function(){k(le,0)};function Z(E){C=E,T||(T=!0,ge())}function je(E,A){P=k(function(){E(e.unstable_now())},A)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(E){E.callback=null},e.unstable_continueExecution=function(){y||g||(y=!0,Z(S))},e.unstable_forceFrameRate=function(E){0>E||125<E?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):_=0<E?Math.floor(1e3/E):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(E){switch(d){case 1:case 2:case 3:var A=3;break;default:A=d}var N=d;d=A;try{return E()}finally{d=N}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(E,A){switch(E){case 1:case 2:case 3:case 4:case 5:break;default:E=3}var N=d;d=E;try{return A()}finally{d=N}},e.unstable_scheduleCallback=function(E,A,N){var R=e.unstable_now();switch(typeof N=="object"&&N!==null?(N=N.delay,N=typeof N=="number"&&0<N?R+N:R):N=R,E){case 1:var W=-1;break;case 2:W=250;break;case 5:W=**********;break;case 4:W=1e4;break;default:W=5e3}return W=N+W,E={id:c++,callback:A,priorityLevel:E,startTime:N,expirationTime:W,sortIndex:-1},N>R?(E.sortIndex=N,t(u,E),n(a)===null&&E===n(u)&&(v?(m(P),P=-1):v=!0,je(x,N-R))):(E.sortIndex=W,t(a,E),y||g||(y=!0,Z(S))),E},e.unstable_shouldYield=re,e.unstable_wrapCallback=function(E){var A=d;return function(){var N=d;d=A;try{return E.apply(this,arguments)}finally{d=N}}}})(Sf);xf.exports=Sf;var um=xf.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cm=D,Re=um;function w(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var wf=new Set,Vr={};function pn(e,t){Un(e,t),Un(e+"Capture",t)}function Un(e,t){for(Vr[e]=t,e=0;e<t.length;e++)wf.add(t[e])}var pt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Es=Object.prototype.hasOwnProperty,fm=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Ja={},qa={};function dm(e){return Es.call(qa,e)?!0:Es.call(Ja,e)?!1:fm.test(e)?qa[e]=!0:(Ja[e]=!0,!1)}function pm(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function hm(e,t,n,r){if(t===null||typeof t>"u"||pm(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function we(e,t,n,r,i,o,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=s}var ce={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ce[e]=new we(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ce[t]=new we(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ce[e]=new we(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ce[e]=new we(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ce[e]=new we(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ce[e]=new we(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ce[e]=new we(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ce[e]=new we(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ce[e]=new we(e,5,!1,e.toLowerCase(),null,!1,!1)});var Ol=/[\-:]([a-z])/g;function zl(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Ol,zl);ce[t]=new we(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Ol,zl);ce[t]=new we(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Ol,zl);ce[t]=new we(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ce[e]=new we(e,1,!1,e.toLowerCase(),null,!1,!1)});ce.xlinkHref=new we("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ce[e]=new we(e,1,!1,e.toLowerCase(),null,!0,!0)});function Il(e,t,n,r){var i=ce.hasOwnProperty(t)?ce[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(hm(t,n,i,r)&&(n=null),r||i===null?dm(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var vt=cm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ii=Symbol.for("react.element"),xn=Symbol.for("react.portal"),Sn=Symbol.for("react.fragment"),Bl=Symbol.for("react.strict_mode"),Vs=Symbol.for("react.profiler"),kf=Symbol.for("react.provider"),Pf=Symbol.for("react.context"),Ul=Symbol.for("react.forward_ref"),Ls=Symbol.for("react.suspense"),As=Symbol.for("react.suspense_list"),$l=Symbol.for("react.memo"),kt=Symbol.for("react.lazy"),Cf=Symbol.for("react.offscreen"),ba=Symbol.iterator;function bn(e){return e===null||typeof e!="object"?null:(e=ba&&e[ba]||e["@@iterator"],typeof e=="function"?e:null)}var X=Object.assign,$o;function ur(e){if($o===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);$o=t&&t[1]||""}return`
`+$o+e}var Wo=!1;function Ho(e,t){if(!e||Wo)return"";Wo=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),o=r.stack.split(`
`),s=i.length-1,l=o.length-1;1<=s&&0<=l&&i[s]!==o[l];)l--;for(;1<=s&&0<=l;s--,l--)if(i[s]!==o[l]){if(s!==1||l!==1)do if(s--,l--,0>l||i[s]!==o[l]){var a=`
`+i[s].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=s&&0<=l);break}}}finally{Wo=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?ur(e):""}function mm(e){switch(e.tag){case 5:return ur(e.type);case 16:return ur("Lazy");case 13:return ur("Suspense");case 19:return ur("SuspenseList");case 0:case 2:case 15:return e=Ho(e.type,!1),e;case 11:return e=Ho(e.type.render,!1),e;case 1:return e=Ho(e.type,!0),e;default:return""}}function Ds(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Sn:return"Fragment";case xn:return"Portal";case Vs:return"Profiler";case Bl:return"StrictMode";case Ls:return"Suspense";case As:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Pf:return(e.displayName||"Context")+".Consumer";case kf:return(e._context.displayName||"Context")+".Provider";case Ul:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case $l:return t=e.displayName||null,t!==null?t:Ds(e.type)||"Memo";case kt:t=e._payload,e=e._init;try{return Ds(e(t))}catch{}}return null}function gm(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Ds(t);case 8:return t===Bl?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function zt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Tf(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function ym(e){var t=Tf(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(s){r=""+s,o.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function oi(e){e._valueTracker||(e._valueTracker=ym(e))}function Ef(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Tf(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function zi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Ms(e,t){var n=t.checked;return X({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function eu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=zt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Vf(e,t){t=t.checked,t!=null&&Il(e,"checked",t,!1)}function Rs(e,t){Vf(e,t);var n=zt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?_s(e,t.type,n):t.hasOwnProperty("defaultValue")&&_s(e,t.type,zt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function tu(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function _s(e,t,n){(t!=="number"||zi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var cr=Array.isArray;function Fn(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+zt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Ns(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(w(91));return X({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function nu(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(w(92));if(cr(n)){if(1<n.length)throw Error(w(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:zt(n)}}function Lf(e,t){var n=zt(t.value),r=zt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function ru(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Af(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Fs(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Af(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var si,Df=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(si=si||document.createElement("div"),si.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=si.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Lr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var hr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},vm=["Webkit","ms","Moz","O"];Object.keys(hr).forEach(function(e){vm.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),hr[t]=hr[e]})});function Mf(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||hr.hasOwnProperty(e)&&hr[e]?(""+t).trim():t+"px"}function Rf(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=Mf(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var xm=X({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function js(e,t){if(t){if(xm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(w(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(w(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(w(61))}if(t.style!=null&&typeof t.style!="object")throw Error(w(62))}}function Os(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var zs=null;function Wl(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Is=null,jn=null,On=null;function iu(e){if(e=Zr(e)){if(typeof Is!="function")throw Error(w(280));var t=e.stateNode;t&&(t=ko(t),Is(e.stateNode,e.type,t))}}function _f(e){jn?On?On.push(e):On=[e]:jn=e}function Nf(){if(jn){var e=jn,t=On;if(On=jn=null,iu(e),t)for(e=0;e<t.length;e++)iu(t[e])}}function Ff(e,t){return e(t)}function jf(){}var Go=!1;function Of(e,t,n){if(Go)return e(t,n);Go=!0;try{return Ff(e,t,n)}finally{Go=!1,(jn!==null||On!==null)&&(jf(),Nf())}}function Ar(e,t){var n=e.stateNode;if(n===null)return null;var r=ko(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(w(231,t,typeof n));return n}var Bs=!1;if(pt)try{var er={};Object.defineProperty(er,"passive",{get:function(){Bs=!0}}),window.addEventListener("test",er,er),window.removeEventListener("test",er,er)}catch{Bs=!1}function Sm(e,t,n,r,i,o,s,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var mr=!1,Ii=null,Bi=!1,Us=null,wm={onError:function(e){mr=!0,Ii=e}};function km(e,t,n,r,i,o,s,l,a){mr=!1,Ii=null,Sm.apply(wm,arguments)}function Pm(e,t,n,r,i,o,s,l,a){if(km.apply(this,arguments),mr){if(mr){var u=Ii;mr=!1,Ii=null}else throw Error(w(198));Bi||(Bi=!0,Us=u)}}function hn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function zf(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function ou(e){if(hn(e)!==e)throw Error(w(188))}function Cm(e){var t=e.alternate;if(!t){if(t=hn(e),t===null)throw Error(w(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return ou(i),e;if(o===r)return ou(i),t;o=o.sibling}throw Error(w(188))}if(n.return!==r.return)n=i,r=o;else{for(var s=!1,l=i.child;l;){if(l===n){s=!0,n=i,r=o;break}if(l===r){s=!0,r=i,n=o;break}l=l.sibling}if(!s){for(l=o.child;l;){if(l===n){s=!0,n=o,r=i;break}if(l===r){s=!0,r=o,n=i;break}l=l.sibling}if(!s)throw Error(w(189))}}if(n.alternate!==r)throw Error(w(190))}if(n.tag!==3)throw Error(w(188));return n.stateNode.current===n?e:t}function If(e){return e=Cm(e),e!==null?Bf(e):null}function Bf(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Bf(e);if(t!==null)return t;e=e.sibling}return null}var Uf=Re.unstable_scheduleCallback,su=Re.unstable_cancelCallback,Tm=Re.unstable_shouldYield,Em=Re.unstable_requestPaint,J=Re.unstable_now,Vm=Re.unstable_getCurrentPriorityLevel,Hl=Re.unstable_ImmediatePriority,$f=Re.unstable_UserBlockingPriority,Ui=Re.unstable_NormalPriority,Lm=Re.unstable_LowPriority,Wf=Re.unstable_IdlePriority,vo=null,nt=null;function Am(e){if(nt&&typeof nt.onCommitFiberRoot=="function")try{nt.onCommitFiberRoot(vo,e,void 0,(e.current.flags&128)===128)}catch{}}var Ye=Math.clz32?Math.clz32:Rm,Dm=Math.log,Mm=Math.LN2;function Rm(e){return e>>>=0,e===0?32:31-(Dm(e)/Mm|0)|0}var li=64,ai=4194304;function fr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function $i(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,s=n&268435455;if(s!==0){var l=s&~i;l!==0?r=fr(l):(o&=s,o!==0&&(r=fr(o)))}else s=n&~i,s!==0?r=fr(s):o!==0&&(r=fr(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,o=t&-t,i>=o||i===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ye(t),i=1<<n,r|=e[n],t&=~i;return r}function _m(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Nm(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var s=31-Ye(o),l=1<<s,a=i[s];a===-1?(!(l&n)||l&r)&&(i[s]=_m(l,t)):a<=t&&(e.expiredLanes|=l),o&=~l}}function $s(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Hf(){var e=li;return li<<=1,!(li&4194240)&&(li=64),e}function Ko(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Xr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ye(t),e[t]=n}function Fm(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-Ye(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}function Gl(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ye(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var z=0;function Gf(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Kf,Kl,Qf,Xf,Yf,Ws=!1,ui=[],At=null,Dt=null,Mt=null,Dr=new Map,Mr=new Map,Tt=[],jm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function lu(e,t){switch(e){case"focusin":case"focusout":At=null;break;case"dragenter":case"dragleave":Dt=null;break;case"mouseover":case"mouseout":Mt=null;break;case"pointerover":case"pointerout":Dr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Mr.delete(t.pointerId)}}function tr(e,t,n,r,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},t!==null&&(t=Zr(t),t!==null&&Kl(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function Om(e,t,n,r,i){switch(t){case"focusin":return At=tr(At,e,t,n,r,i),!0;case"dragenter":return Dt=tr(Dt,e,t,n,r,i),!0;case"mouseover":return Mt=tr(Mt,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return Dr.set(o,tr(Dr.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,Mr.set(o,tr(Mr.get(o)||null,e,t,n,r,i)),!0}return!1}function Zf(e){var t=en(e.target);if(t!==null){var n=hn(t);if(n!==null){if(t=n.tag,t===13){if(t=zf(n),t!==null){e.blockedOn=t,Yf(e.priority,function(){Qf(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ei(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Hs(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);zs=r,n.target.dispatchEvent(r),zs=null}else return t=Zr(n),t!==null&&Kl(t),e.blockedOn=n,!1;t.shift()}return!0}function au(e,t,n){Ei(e)&&n.delete(t)}function zm(){Ws=!1,At!==null&&Ei(At)&&(At=null),Dt!==null&&Ei(Dt)&&(Dt=null),Mt!==null&&Ei(Mt)&&(Mt=null),Dr.forEach(au),Mr.forEach(au)}function nr(e,t){e.blockedOn===t&&(e.blockedOn=null,Ws||(Ws=!0,Re.unstable_scheduleCallback(Re.unstable_NormalPriority,zm)))}function Rr(e){function t(i){return nr(i,e)}if(0<ui.length){nr(ui[0],e);for(var n=1;n<ui.length;n++){var r=ui[n];r.blockedOn===e&&(r.blockedOn=null)}}for(At!==null&&nr(At,e),Dt!==null&&nr(Dt,e),Mt!==null&&nr(Mt,e),Dr.forEach(t),Mr.forEach(t),n=0;n<Tt.length;n++)r=Tt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Tt.length&&(n=Tt[0],n.blockedOn===null);)Zf(n),n.blockedOn===null&&Tt.shift()}var zn=vt.ReactCurrentBatchConfig,Wi=!0;function Im(e,t,n,r){var i=z,o=zn.transition;zn.transition=null;try{z=1,Ql(e,t,n,r)}finally{z=i,zn.transition=o}}function Bm(e,t,n,r){var i=z,o=zn.transition;zn.transition=null;try{z=4,Ql(e,t,n,r)}finally{z=i,zn.transition=o}}function Ql(e,t,n,r){if(Wi){var i=Hs(e,t,n,r);if(i===null)ns(e,t,r,Hi,n),lu(e,r);else if(Om(i,e,t,n,r))r.stopPropagation();else if(lu(e,r),t&4&&-1<jm.indexOf(e)){for(;i!==null;){var o=Zr(i);if(o!==null&&Kf(o),o=Hs(e,t,n,r),o===null&&ns(e,t,r,Hi,n),o===i)break;i=o}i!==null&&r.stopPropagation()}else ns(e,t,r,null,n)}}var Hi=null;function Hs(e,t,n,r){if(Hi=null,e=Wl(r),e=en(e),e!==null)if(t=hn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=zf(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Hi=e,null}function Jf(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Vm()){case Hl:return 1;case $f:return 4;case Ui:case Lm:return 16;case Wf:return 536870912;default:return 16}default:return 16}}var Vt=null,Xl=null,Vi=null;function qf(){if(Vi)return Vi;var e,t=Xl,n=t.length,r,i="value"in Vt?Vt.value:Vt.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===i[o-r];r++);return Vi=i.slice(e,1<r?1-r:void 0)}function Li(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function ci(){return!0}function uu(){return!1}function Fe(e){function t(n,r,i,o,s){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=o,this.target=s,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(o):o[l]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?ci:uu,this.isPropagationStopped=uu,this}return X(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=ci)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=ci)},persist:function(){},isPersistent:ci}),t}var Jn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Yl=Fe(Jn),Yr=X({},Jn,{view:0,detail:0}),Um=Fe(Yr),Qo,Xo,rr,xo=X({},Yr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Zl,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==rr&&(rr&&e.type==="mousemove"?(Qo=e.screenX-rr.screenX,Xo=e.screenY-rr.screenY):Xo=Qo=0,rr=e),Qo)},movementY:function(e){return"movementY"in e?e.movementY:Xo}}),cu=Fe(xo),$m=X({},xo,{dataTransfer:0}),Wm=Fe($m),Hm=X({},Yr,{relatedTarget:0}),Yo=Fe(Hm),Gm=X({},Jn,{animationName:0,elapsedTime:0,pseudoElement:0}),Km=Fe(Gm),Qm=X({},Jn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Xm=Fe(Qm),Ym=X({},Jn,{data:0}),fu=Fe(Ym),Zm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Jm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},qm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function bm(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=qm[e])?!!t[e]:!1}function Zl(){return bm}var eg=X({},Yr,{key:function(e){if(e.key){var t=Zm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Li(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Jm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Zl,charCode:function(e){return e.type==="keypress"?Li(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Li(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),tg=Fe(eg),ng=X({},xo,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),du=Fe(ng),rg=X({},Yr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Zl}),ig=Fe(rg),og=X({},Jn,{propertyName:0,elapsedTime:0,pseudoElement:0}),sg=Fe(og),lg=X({},xo,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),ag=Fe(lg),ug=[9,13,27,32],Jl=pt&&"CompositionEvent"in window,gr=null;pt&&"documentMode"in document&&(gr=document.documentMode);var cg=pt&&"TextEvent"in window&&!gr,bf=pt&&(!Jl||gr&&8<gr&&11>=gr),pu=" ",hu=!1;function ed(e,t){switch(e){case"keyup":return ug.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function td(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var wn=!1;function fg(e,t){switch(e){case"compositionend":return td(t);case"keypress":return t.which!==32?null:(hu=!0,pu);case"textInput":return e=t.data,e===pu&&hu?null:e;default:return null}}function dg(e,t){if(wn)return e==="compositionend"||!Jl&&ed(e,t)?(e=qf(),Vi=Xl=Vt=null,wn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return bf&&t.locale!=="ko"?null:t.data;default:return null}}var pg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function mu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!pg[e.type]:t==="textarea"}function nd(e,t,n,r){_f(r),t=Gi(t,"onChange"),0<t.length&&(n=new Yl("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var yr=null,_r=null;function hg(e){pd(e,0)}function So(e){var t=Cn(e);if(Ef(t))return e}function mg(e,t){if(e==="change")return t}var rd=!1;if(pt){var Zo;if(pt){var Jo="oninput"in document;if(!Jo){var gu=document.createElement("div");gu.setAttribute("oninput","return;"),Jo=typeof gu.oninput=="function"}Zo=Jo}else Zo=!1;rd=Zo&&(!document.documentMode||9<document.documentMode)}function yu(){yr&&(yr.detachEvent("onpropertychange",id),_r=yr=null)}function id(e){if(e.propertyName==="value"&&So(_r)){var t=[];nd(t,_r,e,Wl(e)),Of(hg,t)}}function gg(e,t,n){e==="focusin"?(yu(),yr=t,_r=n,yr.attachEvent("onpropertychange",id)):e==="focusout"&&yu()}function yg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return So(_r)}function vg(e,t){if(e==="click")return So(t)}function xg(e,t){if(e==="input"||e==="change")return So(t)}function Sg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Je=typeof Object.is=="function"?Object.is:Sg;function Nr(e,t){if(Je(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!Es.call(t,i)||!Je(e[i],t[i]))return!1}return!0}function vu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function xu(e,t){var n=vu(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=vu(n)}}function od(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?od(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function sd(){for(var e=window,t=zi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=zi(e.document)}return t}function ql(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function wg(e){var t=sd(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&od(n.ownerDocument.documentElement,n)){if(r!==null&&ql(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=r.end===void 0?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=xu(n,o);var s=xu(n,r);i&&s&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var kg=pt&&"documentMode"in document&&11>=document.documentMode,kn=null,Gs=null,vr=null,Ks=!1;function Su(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ks||kn==null||kn!==zi(r)||(r=kn,"selectionStart"in r&&ql(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),vr&&Nr(vr,r)||(vr=r,r=Gi(Gs,"onSelect"),0<r.length&&(t=new Yl("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=kn)))}function fi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Pn={animationend:fi("Animation","AnimationEnd"),animationiteration:fi("Animation","AnimationIteration"),animationstart:fi("Animation","AnimationStart"),transitionend:fi("Transition","TransitionEnd")},qo={},ld={};pt&&(ld=document.createElement("div").style,"AnimationEvent"in window||(delete Pn.animationend.animation,delete Pn.animationiteration.animation,delete Pn.animationstart.animation),"TransitionEvent"in window||delete Pn.transitionend.transition);function wo(e){if(qo[e])return qo[e];if(!Pn[e])return e;var t=Pn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in ld)return qo[e]=t[n];return e}var ad=wo("animationend"),ud=wo("animationiteration"),cd=wo("animationstart"),fd=wo("transitionend"),dd=new Map,wu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function $t(e,t){dd.set(e,t),pn(t,[e])}for(var bo=0;bo<wu.length;bo++){var es=wu[bo],Pg=es.toLowerCase(),Cg=es[0].toUpperCase()+es.slice(1);$t(Pg,"on"+Cg)}$t(ad,"onAnimationEnd");$t(ud,"onAnimationIteration");$t(cd,"onAnimationStart");$t("dblclick","onDoubleClick");$t("focusin","onFocus");$t("focusout","onBlur");$t(fd,"onTransitionEnd");Un("onMouseEnter",["mouseout","mouseover"]);Un("onMouseLeave",["mouseout","mouseover"]);Un("onPointerEnter",["pointerout","pointerover"]);Un("onPointerLeave",["pointerout","pointerover"]);pn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));pn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));pn("onBeforeInput",["compositionend","keypress","textInput","paste"]);pn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));pn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));pn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var dr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Tg=new Set("cancel close invalid load scroll toggle".split(" ").concat(dr));function ku(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Pm(r,t,void 0,e),e.currentTarget=null}function pd(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var s=r.length-1;0<=s;s--){var l=r[s],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==o&&i.isPropagationStopped())break e;ku(i,l,u),o=a}else for(s=0;s<r.length;s++){if(l=r[s],a=l.instance,u=l.currentTarget,l=l.listener,a!==o&&i.isPropagationStopped())break e;ku(i,l,u),o=a}}}if(Bi)throw e=Us,Bi=!1,Us=null,e}function B(e,t){var n=t[Js];n===void 0&&(n=t[Js]=new Set);var r=e+"__bubble";n.has(r)||(hd(t,e,2,!1),n.add(r))}function ts(e,t,n){var r=0;t&&(r|=4),hd(n,e,r,t)}var di="_reactListening"+Math.random().toString(36).slice(2);function Fr(e){if(!e[di]){e[di]=!0,wf.forEach(function(n){n!=="selectionchange"&&(Tg.has(n)||ts(n,!1,e),ts(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[di]||(t[di]=!0,ts("selectionchange",!1,t))}}function hd(e,t,n,r){switch(Jf(t)){case 1:var i=Im;break;case 4:i=Bm;break;default:i=Ql}n=i.bind(null,t,n,e),i=void 0,!Bs||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function ns(e,t,n,r,i){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var l=r.stateNode.containerInfo;if(l===i||l.nodeType===8&&l.parentNode===i)break;if(s===4)for(s=r.return;s!==null;){var a=s.tag;if((a===3||a===4)&&(a=s.stateNode.containerInfo,a===i||a.nodeType===8&&a.parentNode===i))return;s=s.return}for(;l!==null;){if(s=en(l),s===null)return;if(a=s.tag,a===5||a===6){r=o=s;continue e}l=l.parentNode}}r=r.return}Of(function(){var u=o,c=Wl(n),f=[];e:{var d=dd.get(e);if(d!==void 0){var g=Yl,y=e;switch(e){case"keypress":if(Li(n)===0)break e;case"keydown":case"keyup":g=tg;break;case"focusin":y="focus",g=Yo;break;case"focusout":y="blur",g=Yo;break;case"beforeblur":case"afterblur":g=Yo;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=cu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=Wm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=ig;break;case ad:case ud:case cd:g=Km;break;case fd:g=sg;break;case"scroll":g=Um;break;case"wheel":g=ag;break;case"copy":case"cut":case"paste":g=Xm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=du}var v=(t&4)!==0,k=!v&&e==="scroll",m=v?d!==null?d+"Capture":null:d;v=[];for(var p=u,h;p!==null;){h=p;var x=h.stateNode;if(h.tag===5&&x!==null&&(h=x,m!==null&&(x=Ar(p,m),x!=null&&v.push(jr(p,x,h)))),k)break;p=p.return}0<v.length&&(d=new g(d,y,null,n,c),f.push({event:d,listeners:v}))}}if(!(t&7)){e:{if(d=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",d&&n!==zs&&(y=n.relatedTarget||n.fromElement)&&(en(y)||y[ht]))break e;if((g||d)&&(d=c.window===c?c:(d=c.ownerDocument)?d.defaultView||d.parentWindow:window,g?(y=n.relatedTarget||n.toElement,g=u,y=y?en(y):null,y!==null&&(k=hn(y),y!==k||y.tag!==5&&y.tag!==6)&&(y=null)):(g=null,y=u),g!==y)){if(v=cu,x="onMouseLeave",m="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(v=du,x="onPointerLeave",m="onPointerEnter",p="pointer"),k=g==null?d:Cn(g),h=y==null?d:Cn(y),d=new v(x,p+"leave",g,n,c),d.target=k,d.relatedTarget=h,x=null,en(c)===u&&(v=new v(m,p+"enter",y,n,c),v.target=h,v.relatedTarget=k,x=v),k=x,g&&y)t:{for(v=g,m=y,p=0,h=v;h;h=vn(h))p++;for(h=0,x=m;x;x=vn(x))h++;for(;0<p-h;)v=vn(v),p--;for(;0<h-p;)m=vn(m),h--;for(;p--;){if(v===m||m!==null&&v===m.alternate)break t;v=vn(v),m=vn(m)}v=null}else v=null;g!==null&&Pu(f,d,g,v,!1),y!==null&&k!==null&&Pu(f,k,y,v,!0)}}e:{if(d=u?Cn(u):window,g=d.nodeName&&d.nodeName.toLowerCase(),g==="select"||g==="input"&&d.type==="file")var S=mg;else if(mu(d))if(rd)S=xg;else{S=yg;var T=gg}else(g=d.nodeName)&&g.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(S=vg);if(S&&(S=S(e,u))){nd(f,S,n,c);break e}T&&T(e,d,u),e==="focusout"&&(T=d._wrapperState)&&T.controlled&&d.type==="number"&&_s(d,"number",d.value)}switch(T=u?Cn(u):window,e){case"focusin":(mu(T)||T.contentEditable==="true")&&(kn=T,Gs=u,vr=null);break;case"focusout":vr=Gs=kn=null;break;case"mousedown":Ks=!0;break;case"contextmenu":case"mouseup":case"dragend":Ks=!1,Su(f,n,c);break;case"selectionchange":if(kg)break;case"keydown":case"keyup":Su(f,n,c)}var C;if(Jl)e:{switch(e){case"compositionstart":var P="onCompositionStart";break e;case"compositionend":P="onCompositionEnd";break e;case"compositionupdate":P="onCompositionUpdate";break e}P=void 0}else wn?ed(e,n)&&(P="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(P="onCompositionStart");P&&(bf&&n.locale!=="ko"&&(wn||P!=="onCompositionStart"?P==="onCompositionEnd"&&wn&&(C=qf()):(Vt=c,Xl="value"in Vt?Vt.value:Vt.textContent,wn=!0)),T=Gi(u,P),0<T.length&&(P=new fu(P,e,null,n,c),f.push({event:P,listeners:T}),C?P.data=C:(C=td(n),C!==null&&(P.data=C)))),(C=cg?fg(e,n):dg(e,n))&&(u=Gi(u,"onBeforeInput"),0<u.length&&(c=new fu("onBeforeInput","beforeinput",null,n,c),f.push({event:c,listeners:u}),c.data=C))}pd(f,t)})}function jr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Gi(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,o=i.stateNode;i.tag===5&&o!==null&&(i=o,o=Ar(e,n),o!=null&&r.unshift(jr(e,o,i)),o=Ar(e,t),o!=null&&r.push(jr(e,o,i))),e=e.return}return r}function vn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Pu(e,t,n,r,i){for(var o=t._reactName,s=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,i?(a=Ar(n,o),a!=null&&s.unshift(jr(n,a,l))):i||(a=Ar(n,o),a!=null&&s.push(jr(n,a,l)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var Eg=/\r\n?/g,Vg=/\u0000|\uFFFD/g;function Cu(e){return(typeof e=="string"?e:""+e).replace(Eg,`
`).replace(Vg,"")}function pi(e,t,n){if(t=Cu(t),Cu(e)!==t&&n)throw Error(w(425))}function Ki(){}var Qs=null,Xs=null;function Ys(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Zs=typeof setTimeout=="function"?setTimeout:void 0,Lg=typeof clearTimeout=="function"?clearTimeout:void 0,Tu=typeof Promise=="function"?Promise:void 0,Ag=typeof queueMicrotask=="function"?queueMicrotask:typeof Tu<"u"?function(e){return Tu.resolve(null).then(e).catch(Dg)}:Zs;function Dg(e){setTimeout(function(){throw e})}function rs(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),Rr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Rr(t)}function Rt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Eu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var qn=Math.random().toString(36).slice(2),tt="__reactFiber$"+qn,Or="__reactProps$"+qn,ht="__reactContainer$"+qn,Js="__reactEvents$"+qn,Mg="__reactListeners$"+qn,Rg="__reactHandles$"+qn;function en(e){var t=e[tt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ht]||n[tt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Eu(e);e!==null;){if(n=e[tt])return n;e=Eu(e)}return t}e=n,n=e.parentNode}return null}function Zr(e){return e=e[tt]||e[ht],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Cn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(w(33))}function ko(e){return e[Or]||null}var qs=[],Tn=-1;function Wt(e){return{current:e}}function U(e){0>Tn||(e.current=qs[Tn],qs[Tn]=null,Tn--)}function I(e,t){Tn++,qs[Tn]=e.current,e.current=t}var It={},me=Wt(It),Ce=Wt(!1),an=It;function $n(e,t){var n=e.type.contextTypes;if(!n)return It;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},o;for(o in n)i[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Te(e){return e=e.childContextTypes,e!=null}function Qi(){U(Ce),U(me)}function Vu(e,t,n){if(me.current!==It)throw Error(w(168));I(me,t),I(Ce,n)}function md(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(w(108,gm(e)||"Unknown",i));return X({},n,r)}function Xi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||It,an=me.current,I(me,e),I(Ce,Ce.current),!0}function Lu(e,t,n){var r=e.stateNode;if(!r)throw Error(w(169));n?(e=md(e,t,an),r.__reactInternalMemoizedMergedChildContext=e,U(Ce),U(me),I(me,e)):U(Ce),I(Ce,n)}var st=null,Po=!1,is=!1;function gd(e){st===null?st=[e]:st.push(e)}function _g(e){Po=!0,gd(e)}function Ht(){if(!is&&st!==null){is=!0;var e=0,t=z;try{var n=st;for(z=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}st=null,Po=!1}catch(i){throw st!==null&&(st=st.slice(e+1)),Uf(Hl,Ht),i}finally{z=t,is=!1}}return null}var En=[],Vn=0,Yi=null,Zi=0,Ie=[],Be=0,un=null,lt=1,at="";function Zt(e,t){En[Vn++]=Zi,En[Vn++]=Yi,Yi=e,Zi=t}function yd(e,t,n){Ie[Be++]=lt,Ie[Be++]=at,Ie[Be++]=un,un=e;var r=lt;e=at;var i=32-Ye(r)-1;r&=~(1<<i),n+=1;var o=32-Ye(t)+i;if(30<o){var s=i-i%5;o=(r&(1<<s)-1).toString(32),r>>=s,i-=s,lt=1<<32-Ye(t)+i|n<<i|r,at=o+e}else lt=1<<o|n<<i|r,at=e}function bl(e){e.return!==null&&(Zt(e,1),yd(e,1,0))}function ea(e){for(;e===Yi;)Yi=En[--Vn],En[Vn]=null,Zi=En[--Vn],En[Vn]=null;for(;e===un;)un=Ie[--Be],Ie[Be]=null,at=Ie[--Be],Ie[Be]=null,lt=Ie[--Be],Ie[Be]=null}var Me=null,De=null,H=!1,Xe=null;function vd(e,t){var n=Ue(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Au(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Me=e,De=Rt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Me=e,De=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=un!==null?{id:lt,overflow:at}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ue(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Me=e,De=null,!0):!1;default:return!1}}function bs(e){return(e.mode&1)!==0&&(e.flags&128)===0}function el(e){if(H){var t=De;if(t){var n=t;if(!Au(e,t)){if(bs(e))throw Error(w(418));t=Rt(n.nextSibling);var r=Me;t&&Au(e,t)?vd(r,n):(e.flags=e.flags&-4097|2,H=!1,Me=e)}}else{if(bs(e))throw Error(w(418));e.flags=e.flags&-4097|2,H=!1,Me=e}}}function Du(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Me=e}function hi(e){if(e!==Me)return!1;if(!H)return Du(e),H=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ys(e.type,e.memoizedProps)),t&&(t=De)){if(bs(e))throw xd(),Error(w(418));for(;t;)vd(e,t),t=Rt(t.nextSibling)}if(Du(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(w(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){De=Rt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}De=null}}else De=Me?Rt(e.stateNode.nextSibling):null;return!0}function xd(){for(var e=De;e;)e=Rt(e.nextSibling)}function Wn(){De=Me=null,H=!1}function ta(e){Xe===null?Xe=[e]:Xe.push(e)}var Ng=vt.ReactCurrentBatchConfig;function ir(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(w(309));var r=n.stateNode}if(!r)throw Error(w(147,e));var i=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(s){var l=i.refs;s===null?delete l[o]:l[o]=s},t._stringRef=o,t)}if(typeof e!="string")throw Error(w(284));if(!n._owner)throw Error(w(290,e))}return e}function mi(e,t){throw e=Object.prototype.toString.call(t),Error(w(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Mu(e){var t=e._init;return t(e._payload)}function Sd(e){function t(m,p){if(e){var h=m.deletions;h===null?(m.deletions=[p],m.flags|=16):h.push(p)}}function n(m,p){if(!e)return null;for(;p!==null;)t(m,p),p=p.sibling;return null}function r(m,p){for(m=new Map;p!==null;)p.key!==null?m.set(p.key,p):m.set(p.index,p),p=p.sibling;return m}function i(m,p){return m=jt(m,p),m.index=0,m.sibling=null,m}function o(m,p,h){return m.index=h,e?(h=m.alternate,h!==null?(h=h.index,h<p?(m.flags|=2,p):h):(m.flags|=2,p)):(m.flags|=1048576,p)}function s(m){return e&&m.alternate===null&&(m.flags|=2),m}function l(m,p,h,x){return p===null||p.tag!==6?(p=fs(h,m.mode,x),p.return=m,p):(p=i(p,h),p.return=m,p)}function a(m,p,h,x){var S=h.type;return S===Sn?c(m,p,h.props.children,x,h.key):p!==null&&(p.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===kt&&Mu(S)===p.type)?(x=i(p,h.props),x.ref=ir(m,p,h),x.return=m,x):(x=Fi(h.type,h.key,h.props,null,m.mode,x),x.ref=ir(m,p,h),x.return=m,x)}function u(m,p,h,x){return p===null||p.tag!==4||p.stateNode.containerInfo!==h.containerInfo||p.stateNode.implementation!==h.implementation?(p=ds(h,m.mode,x),p.return=m,p):(p=i(p,h.children||[]),p.return=m,p)}function c(m,p,h,x,S){return p===null||p.tag!==7?(p=sn(h,m.mode,x,S),p.return=m,p):(p=i(p,h),p.return=m,p)}function f(m,p,h){if(typeof p=="string"&&p!==""||typeof p=="number")return p=fs(""+p,m.mode,h),p.return=m,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case ii:return h=Fi(p.type,p.key,p.props,null,m.mode,h),h.ref=ir(m,null,p),h.return=m,h;case xn:return p=ds(p,m.mode,h),p.return=m,p;case kt:var x=p._init;return f(m,x(p._payload),h)}if(cr(p)||bn(p))return p=sn(p,m.mode,h,null),p.return=m,p;mi(m,p)}return null}function d(m,p,h,x){var S=p!==null?p.key:null;if(typeof h=="string"&&h!==""||typeof h=="number")return S!==null?null:l(m,p,""+h,x);if(typeof h=="object"&&h!==null){switch(h.$$typeof){case ii:return h.key===S?a(m,p,h,x):null;case xn:return h.key===S?u(m,p,h,x):null;case kt:return S=h._init,d(m,p,S(h._payload),x)}if(cr(h)||bn(h))return S!==null?null:c(m,p,h,x,null);mi(m,h)}return null}function g(m,p,h,x,S){if(typeof x=="string"&&x!==""||typeof x=="number")return m=m.get(h)||null,l(p,m,""+x,S);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case ii:return m=m.get(x.key===null?h:x.key)||null,a(p,m,x,S);case xn:return m=m.get(x.key===null?h:x.key)||null,u(p,m,x,S);case kt:var T=x._init;return g(m,p,h,T(x._payload),S)}if(cr(x)||bn(x))return m=m.get(h)||null,c(p,m,x,S,null);mi(p,x)}return null}function y(m,p,h,x){for(var S=null,T=null,C=p,P=p=0,_=null;C!==null&&P<h.length;P++){C.index>P?(_=C,C=null):_=C.sibling;var M=d(m,C,h[P],x);if(M===null){C===null&&(C=_);break}e&&C&&M.alternate===null&&t(m,C),p=o(M,p,P),T===null?S=M:T.sibling=M,T=M,C=_}if(P===h.length)return n(m,C),H&&Zt(m,P),S;if(C===null){for(;P<h.length;P++)C=f(m,h[P],x),C!==null&&(p=o(C,p,P),T===null?S=C:T.sibling=C,T=C);return H&&Zt(m,P),S}for(C=r(m,C);P<h.length;P++)_=g(C,m,P,h[P],x),_!==null&&(e&&_.alternate!==null&&C.delete(_.key===null?P:_.key),p=o(_,p,P),T===null?S=_:T.sibling=_,T=_);return e&&C.forEach(function(re){return t(m,re)}),H&&Zt(m,P),S}function v(m,p,h,x){var S=bn(h);if(typeof S!="function")throw Error(w(150));if(h=S.call(h),h==null)throw Error(w(151));for(var T=S=null,C=p,P=p=0,_=null,M=h.next();C!==null&&!M.done;P++,M=h.next()){C.index>P?(_=C,C=null):_=C.sibling;var re=d(m,C,M.value,x);if(re===null){C===null&&(C=_);break}e&&C&&re.alternate===null&&t(m,C),p=o(re,p,P),T===null?S=re:T.sibling=re,T=re,C=_}if(M.done)return n(m,C),H&&Zt(m,P),S;if(C===null){for(;!M.done;P++,M=h.next())M=f(m,M.value,x),M!==null&&(p=o(M,p,P),T===null?S=M:T.sibling=M,T=M);return H&&Zt(m,P),S}for(C=r(m,C);!M.done;P++,M=h.next())M=g(C,m,P,M.value,x),M!==null&&(e&&M.alternate!==null&&C.delete(M.key===null?P:M.key),p=o(M,p,P),T===null?S=M:T.sibling=M,T=M);return e&&C.forEach(function(le){return t(m,le)}),H&&Zt(m,P),S}function k(m,p,h,x){if(typeof h=="object"&&h!==null&&h.type===Sn&&h.key===null&&(h=h.props.children),typeof h=="object"&&h!==null){switch(h.$$typeof){case ii:e:{for(var S=h.key,T=p;T!==null;){if(T.key===S){if(S=h.type,S===Sn){if(T.tag===7){n(m,T.sibling),p=i(T,h.props.children),p.return=m,m=p;break e}}else if(T.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===kt&&Mu(S)===T.type){n(m,T.sibling),p=i(T,h.props),p.ref=ir(m,T,h),p.return=m,m=p;break e}n(m,T);break}else t(m,T);T=T.sibling}h.type===Sn?(p=sn(h.props.children,m.mode,x,h.key),p.return=m,m=p):(x=Fi(h.type,h.key,h.props,null,m.mode,x),x.ref=ir(m,p,h),x.return=m,m=x)}return s(m);case xn:e:{for(T=h.key;p!==null;){if(p.key===T)if(p.tag===4&&p.stateNode.containerInfo===h.containerInfo&&p.stateNode.implementation===h.implementation){n(m,p.sibling),p=i(p,h.children||[]),p.return=m,m=p;break e}else{n(m,p);break}else t(m,p);p=p.sibling}p=ds(h,m.mode,x),p.return=m,m=p}return s(m);case kt:return T=h._init,k(m,p,T(h._payload),x)}if(cr(h))return y(m,p,h,x);if(bn(h))return v(m,p,h,x);mi(m,h)}return typeof h=="string"&&h!==""||typeof h=="number"?(h=""+h,p!==null&&p.tag===6?(n(m,p.sibling),p=i(p,h),p.return=m,m=p):(n(m,p),p=fs(h,m.mode,x),p.return=m,m=p),s(m)):n(m,p)}return k}var Hn=Sd(!0),wd=Sd(!1),Ji=Wt(null),qi=null,Ln=null,na=null;function ra(){na=Ln=qi=null}function ia(e){var t=Ji.current;U(Ji),e._currentValue=t}function tl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function In(e,t){qi=e,na=Ln=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Pe=!0),e.firstContext=null)}function We(e){var t=e._currentValue;if(na!==e)if(e={context:e,memoizedValue:t,next:null},Ln===null){if(qi===null)throw Error(w(308));Ln=e,qi.dependencies={lanes:0,firstContext:e}}else Ln=Ln.next=e;return t}var tn=null;function oa(e){tn===null?tn=[e]:tn.push(e)}function kd(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,oa(t)):(n.next=i.next,i.next=n),t.interleaved=n,mt(e,r)}function mt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Pt=!1;function sa(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Pd(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function ct(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function _t(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,j&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,mt(e,n)}return i=r.interleaved,i===null?(t.next=t,oa(r)):(t.next=i.next,i.next=t),r.interleaved=t,mt(e,n)}function Ai(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Gl(e,n)}}function Ru(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?i=o=s:o=o.next=s,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function bi(e,t,n,r){var i=e.updateQueue;Pt=!1;var o=i.firstBaseUpdate,s=i.lastBaseUpdate,l=i.shared.pending;if(l!==null){i.shared.pending=null;var a=l,u=a.next;a.next=null,s===null?o=u:s.next=u,s=a;var c=e.alternate;c!==null&&(c=c.updateQueue,l=c.lastBaseUpdate,l!==s&&(l===null?c.firstBaseUpdate=u:l.next=u,c.lastBaseUpdate=a))}if(o!==null){var f=i.baseState;s=0,c=u=a=null,l=o;do{var d=l.lane,g=l.eventTime;if((r&d)===d){c!==null&&(c=c.next={eventTime:g,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var y=e,v=l;switch(d=t,g=n,v.tag){case 1:if(y=v.payload,typeof y=="function"){f=y.call(g,f,d);break e}f=y;break e;case 3:y.flags=y.flags&-65537|128;case 0:if(y=v.payload,d=typeof y=="function"?y.call(g,f,d):y,d==null)break e;f=X({},f,d);break e;case 2:Pt=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,d=i.effects,d===null?i.effects=[l]:d.push(l))}else g={eventTime:g,lane:d,tag:l.tag,payload:l.payload,callback:l.callback,next:null},c===null?(u=c=g,a=f):c=c.next=g,s|=d;if(l=l.next,l===null){if(l=i.shared.pending,l===null)break;d=l,l=d.next,d.next=null,i.lastBaseUpdate=d,i.shared.pending=null}}while(!0);if(c===null&&(a=f),i.baseState=a,i.firstBaseUpdate=u,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do s|=i.lane,i=i.next;while(i!==t)}else o===null&&(i.shared.lanes=0);fn|=s,e.lanes=s,e.memoizedState=f}}function _u(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(w(191,i));i.call(r)}}}var Jr={},rt=Wt(Jr),zr=Wt(Jr),Ir=Wt(Jr);function nn(e){if(e===Jr)throw Error(w(174));return e}function la(e,t){switch(I(Ir,t),I(zr,e),I(rt,Jr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Fs(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Fs(t,e)}U(rt),I(rt,t)}function Gn(){U(rt),U(zr),U(Ir)}function Cd(e){nn(Ir.current);var t=nn(rt.current),n=Fs(t,e.type);t!==n&&(I(zr,e),I(rt,n))}function aa(e){zr.current===e&&(U(rt),U(zr))}var G=Wt(0);function eo(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var os=[];function ua(){for(var e=0;e<os.length;e++)os[e]._workInProgressVersionPrimary=null;os.length=0}var Di=vt.ReactCurrentDispatcher,ss=vt.ReactCurrentBatchConfig,cn=0,Q=null,te=null,oe=null,to=!1,xr=!1,Br=0,Fg=0;function fe(){throw Error(w(321))}function ca(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Je(e[n],t[n]))return!1;return!0}function fa(e,t,n,r,i,o){if(cn=o,Q=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Di.current=e===null||e.memoizedState===null?Ig:Bg,e=n(r,i),xr){o=0;do{if(xr=!1,Br=0,25<=o)throw Error(w(301));o+=1,oe=te=null,t.updateQueue=null,Di.current=Ug,e=n(r,i)}while(xr)}if(Di.current=no,t=te!==null&&te.next!==null,cn=0,oe=te=Q=null,to=!1,t)throw Error(w(300));return e}function da(){var e=Br!==0;return Br=0,e}function et(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return oe===null?Q.memoizedState=oe=e:oe=oe.next=e,oe}function He(){if(te===null){var e=Q.alternate;e=e!==null?e.memoizedState:null}else e=te.next;var t=oe===null?Q.memoizedState:oe.next;if(t!==null)oe=t,te=e;else{if(e===null)throw Error(w(310));te=e,e={memoizedState:te.memoizedState,baseState:te.baseState,baseQueue:te.baseQueue,queue:te.queue,next:null},oe===null?Q.memoizedState=oe=e:oe=oe.next=e}return oe}function Ur(e,t){return typeof t=="function"?t(e):t}function ls(e){var t=He(),n=t.queue;if(n===null)throw Error(w(311));n.lastRenderedReducer=e;var r=te,i=r.baseQueue,o=n.pending;if(o!==null){if(i!==null){var s=i.next;i.next=o.next,o.next=s}r.baseQueue=i=o,n.pending=null}if(i!==null){o=i.next,r=r.baseState;var l=s=null,a=null,u=o;do{var c=u.lane;if((cn&c)===c)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=f,s=r):a=a.next=f,Q.lanes|=c,fn|=c}u=u.next}while(u!==null&&u!==o);a===null?s=r:a.next=l,Je(r,t.memoizedState)||(Pe=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do o=i.lane,Q.lanes|=o,fn|=o,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function as(e){var t=He(),n=t.queue;if(n===null)throw Error(w(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var s=i=i.next;do o=e(o,s.action),s=s.next;while(s!==i);Je(o,t.memoizedState)||(Pe=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function Td(){}function Ed(e,t){var n=Q,r=He(),i=t(),o=!Je(r.memoizedState,i);if(o&&(r.memoizedState=i,Pe=!0),r=r.queue,pa(Ad.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||oe!==null&&oe.memoizedState.tag&1){if(n.flags|=2048,$r(9,Ld.bind(null,n,r,i,t),void 0,null),se===null)throw Error(w(349));cn&30||Vd(n,t,i)}return i}function Vd(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Q.updateQueue,t===null?(t={lastEffect:null,stores:null},Q.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Ld(e,t,n,r){t.value=n,t.getSnapshot=r,Dd(t)&&Md(e)}function Ad(e,t,n){return n(function(){Dd(t)&&Md(e)})}function Dd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Je(e,n)}catch{return!0}}function Md(e){var t=mt(e,1);t!==null&&Ze(t,e,1,-1)}function Nu(e){var t=et();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ur,lastRenderedState:e},t.queue=e,e=e.dispatch=zg.bind(null,Q,e),[t.memoizedState,e]}function $r(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Q.updateQueue,t===null?(t={lastEffect:null,stores:null},Q.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Rd(){return He().memoizedState}function Mi(e,t,n,r){var i=et();Q.flags|=e,i.memoizedState=$r(1|t,n,void 0,r===void 0?null:r)}function Co(e,t,n,r){var i=He();r=r===void 0?null:r;var o=void 0;if(te!==null){var s=te.memoizedState;if(o=s.destroy,r!==null&&ca(r,s.deps)){i.memoizedState=$r(t,n,o,r);return}}Q.flags|=e,i.memoizedState=$r(1|t,n,o,r)}function Fu(e,t){return Mi(8390656,8,e,t)}function pa(e,t){return Co(2048,8,e,t)}function _d(e,t){return Co(4,2,e,t)}function Nd(e,t){return Co(4,4,e,t)}function Fd(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function jd(e,t,n){return n=n!=null?n.concat([e]):null,Co(4,4,Fd.bind(null,t,e),n)}function ha(){}function Od(e,t){var n=He();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ca(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function zd(e,t){var n=He();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ca(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Id(e,t,n){return cn&21?(Je(n,t)||(n=Hf(),Q.lanes|=n,fn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Pe=!0),e.memoizedState=n)}function jg(e,t){var n=z;z=n!==0&&4>n?n:4,e(!0);var r=ss.transition;ss.transition={};try{e(!1),t()}finally{z=n,ss.transition=r}}function Bd(){return He().memoizedState}function Og(e,t,n){var r=Ft(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Ud(e))$d(t,n);else if(n=kd(e,t,n,r),n!==null){var i=xe();Ze(n,e,r,i),Wd(n,t,r)}}function zg(e,t,n){var r=Ft(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ud(e))$d(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var s=t.lastRenderedState,l=o(s,n);if(i.hasEagerState=!0,i.eagerState=l,Je(l,s)){var a=t.interleaved;a===null?(i.next=i,oa(t)):(i.next=a.next,a.next=i),t.interleaved=i;return}}catch{}finally{}n=kd(e,t,i,r),n!==null&&(i=xe(),Ze(n,e,r,i),Wd(n,t,r))}}function Ud(e){var t=e.alternate;return e===Q||t!==null&&t===Q}function $d(e,t){xr=to=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Wd(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Gl(e,n)}}var no={readContext:We,useCallback:fe,useContext:fe,useEffect:fe,useImperativeHandle:fe,useInsertionEffect:fe,useLayoutEffect:fe,useMemo:fe,useReducer:fe,useRef:fe,useState:fe,useDebugValue:fe,useDeferredValue:fe,useTransition:fe,useMutableSource:fe,useSyncExternalStore:fe,useId:fe,unstable_isNewReconciler:!1},Ig={readContext:We,useCallback:function(e,t){return et().memoizedState=[e,t===void 0?null:t],e},useContext:We,useEffect:Fu,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Mi(4194308,4,Fd.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Mi(4194308,4,e,t)},useInsertionEffect:function(e,t){return Mi(4,2,e,t)},useMemo:function(e,t){var n=et();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=et();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Og.bind(null,Q,e),[r.memoizedState,e]},useRef:function(e){var t=et();return e={current:e},t.memoizedState=e},useState:Nu,useDebugValue:ha,useDeferredValue:function(e){return et().memoizedState=e},useTransition:function(){var e=Nu(!1),t=e[0];return e=jg.bind(null,e[1]),et().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Q,i=et();if(H){if(n===void 0)throw Error(w(407));n=n()}else{if(n=t(),se===null)throw Error(w(349));cn&30||Vd(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,Fu(Ad.bind(null,r,o,e),[e]),r.flags|=2048,$r(9,Ld.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=et(),t=se.identifierPrefix;if(H){var n=at,r=lt;n=(r&~(1<<32-Ye(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Br++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Fg++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Bg={readContext:We,useCallback:Od,useContext:We,useEffect:pa,useImperativeHandle:jd,useInsertionEffect:_d,useLayoutEffect:Nd,useMemo:zd,useReducer:ls,useRef:Rd,useState:function(){return ls(Ur)},useDebugValue:ha,useDeferredValue:function(e){var t=He();return Id(t,te.memoizedState,e)},useTransition:function(){var e=ls(Ur)[0],t=He().memoizedState;return[e,t]},useMutableSource:Td,useSyncExternalStore:Ed,useId:Bd,unstable_isNewReconciler:!1},Ug={readContext:We,useCallback:Od,useContext:We,useEffect:pa,useImperativeHandle:jd,useInsertionEffect:_d,useLayoutEffect:Nd,useMemo:zd,useReducer:as,useRef:Rd,useState:function(){return as(Ur)},useDebugValue:ha,useDeferredValue:function(e){var t=He();return te===null?t.memoizedState=e:Id(t,te.memoizedState,e)},useTransition:function(){var e=as(Ur)[0],t=He().memoizedState;return[e,t]},useMutableSource:Td,useSyncExternalStore:Ed,useId:Bd,unstable_isNewReconciler:!1};function Ke(e,t){if(e&&e.defaultProps){t=X({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function nl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:X({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var To={isMounted:function(e){return(e=e._reactInternals)?hn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=xe(),i=Ft(e),o=ct(r,i);o.payload=t,n!=null&&(o.callback=n),t=_t(e,o,i),t!==null&&(Ze(t,e,i,r),Ai(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=xe(),i=Ft(e),o=ct(r,i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=_t(e,o,i),t!==null&&(Ze(t,e,i,r),Ai(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=xe(),r=Ft(e),i=ct(n,r);i.tag=2,t!=null&&(i.callback=t),t=_t(e,i,r),t!==null&&(Ze(t,e,r,n),Ai(t,e,r))}};function ju(e,t,n,r,i,o,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,s):t.prototype&&t.prototype.isPureReactComponent?!Nr(n,r)||!Nr(i,o):!0}function Hd(e,t,n){var r=!1,i=It,o=t.contextType;return typeof o=="object"&&o!==null?o=We(o):(i=Te(t)?an:me.current,r=t.contextTypes,o=(r=r!=null)?$n(e,i):It),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=To,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function Ou(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&To.enqueueReplaceState(t,t.state,null)}function rl(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},sa(e);var o=t.contextType;typeof o=="object"&&o!==null?i.context=We(o):(o=Te(t)?an:me.current,i.context=$n(e,o)),i.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(nl(e,t,o,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&To.enqueueReplaceState(i,i.state,null),bi(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function Kn(e,t){try{var n="",r=t;do n+=mm(r),r=r.return;while(r);var i=n}catch(o){i=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:i,digest:null}}function us(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function il(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var $g=typeof WeakMap=="function"?WeakMap:Map;function Gd(e,t,n){n=ct(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){io||(io=!0,hl=r),il(e,t)},n}function Kd(e,t,n){n=ct(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){il(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){il(e,t),typeof r!="function"&&(Nt===null?Nt=new Set([this]):Nt.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function zu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new $g;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=ny.bind(null,e,t,n),t.then(e,e))}function Iu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Bu(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=ct(-1,1),t.tag=2,_t(n,t,1))),n.lanes|=1),e)}var Wg=vt.ReactCurrentOwner,Pe=!1;function ve(e,t,n,r){t.child=e===null?wd(t,null,n,r):Hn(t,e.child,n,r)}function Uu(e,t,n,r,i){n=n.render;var o=t.ref;return In(t,i),r=fa(e,t,n,r,o,i),n=da(),e!==null&&!Pe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,gt(e,t,i)):(H&&n&&bl(t),t.flags|=1,ve(e,t,r,i),t.child)}function $u(e,t,n,r,i){if(e===null){var o=n.type;return typeof o=="function"&&!ka(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,Qd(e,t,o,r,i)):(e=Fi(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&i)){var s=o.memoizedProps;if(n=n.compare,n=n!==null?n:Nr,n(s,r)&&e.ref===t.ref)return gt(e,t,i)}return t.flags|=1,e=jt(o,r),e.ref=t.ref,e.return=t,t.child=e}function Qd(e,t,n,r,i){if(e!==null){var o=e.memoizedProps;if(Nr(o,r)&&e.ref===t.ref)if(Pe=!1,t.pendingProps=r=o,(e.lanes&i)!==0)e.flags&131072&&(Pe=!0);else return t.lanes=e.lanes,gt(e,t,i)}return ol(e,t,n,r,i)}function Xd(e,t,n){var r=t.pendingProps,i=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},I(Dn,Ae),Ae|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,I(Dn,Ae),Ae|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,I(Dn,Ae),Ae|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,I(Dn,Ae),Ae|=r;return ve(e,t,i,n),t.child}function Yd(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function ol(e,t,n,r,i){var o=Te(n)?an:me.current;return o=$n(t,o),In(t,i),n=fa(e,t,n,r,o,i),r=da(),e!==null&&!Pe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,gt(e,t,i)):(H&&r&&bl(t),t.flags|=1,ve(e,t,n,i),t.child)}function Wu(e,t,n,r,i){if(Te(n)){var o=!0;Xi(t)}else o=!1;if(In(t,i),t.stateNode===null)Ri(e,t),Hd(t,n,r),rl(t,n,r,i),r=!0;else if(e===null){var s=t.stateNode,l=t.memoizedProps;s.props=l;var a=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=We(u):(u=Te(n)?an:me.current,u=$n(t,u));var c=n.getDerivedStateFromProps,f=typeof c=="function"||typeof s.getSnapshotBeforeUpdate=="function";f||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==r||a!==u)&&Ou(t,s,r,u),Pt=!1;var d=t.memoizedState;s.state=d,bi(t,r,s,i),a=t.memoizedState,l!==r||d!==a||Ce.current||Pt?(typeof c=="function"&&(nl(t,n,c,r),a=t.memoizedState),(l=Pt||ju(t,n,l,r,d,a,u))?(f||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),s.props=r,s.state=a,s.context=u,r=l):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,Pd(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:Ke(t.type,l),s.props=u,f=t.pendingProps,d=s.context,a=n.contextType,typeof a=="object"&&a!==null?a=We(a):(a=Te(n)?an:me.current,a=$n(t,a));var g=n.getDerivedStateFromProps;(c=typeof g=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==f||d!==a)&&Ou(t,s,r,a),Pt=!1,d=t.memoizedState,s.state=d,bi(t,r,s,i);var y=t.memoizedState;l!==f||d!==y||Ce.current||Pt?(typeof g=="function"&&(nl(t,n,g,r),y=t.memoizedState),(u=Pt||ju(t,n,u,r,d,y,a)||!1)?(c||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,y,a),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,y,a)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=y),s.props=r,s.state=y,s.context=a,r=u):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return sl(e,t,n,r,o,i)}function sl(e,t,n,r,i,o){Yd(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return i&&Lu(t,n,!1),gt(e,t,o);r=t.stateNode,Wg.current=t;var l=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=Hn(t,e.child,null,o),t.child=Hn(t,null,l,o)):ve(e,t,l,o),t.memoizedState=r.state,i&&Lu(t,n,!0),t.child}function Zd(e){var t=e.stateNode;t.pendingContext?Vu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Vu(e,t.context,!1),la(e,t.containerInfo)}function Hu(e,t,n,r,i){return Wn(),ta(i),t.flags|=256,ve(e,t,n,r),t.child}var ll={dehydrated:null,treeContext:null,retryLane:0};function al(e){return{baseLanes:e,cachePool:null,transitions:null}}function Jd(e,t,n){var r=t.pendingProps,i=G.current,o=!1,s=(t.flags&128)!==0,l;if((l=s)||(l=e!==null&&e.memoizedState===null?!1:(i&2)!==0),l?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),I(G,i&1),e===null)return el(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,o?(r=t.mode,o=t.child,s={mode:"hidden",children:s},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=s):o=Lo(s,r,0,null),e=sn(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=al(n),t.memoizedState=ll,e):ma(t,s));if(i=e.memoizedState,i!==null&&(l=i.dehydrated,l!==null))return Hg(e,t,s,r,l,i,n);if(o){o=r.fallback,s=t.mode,i=e.child,l=i.sibling;var a={mode:"hidden",children:r.children};return!(s&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=jt(i,a),r.subtreeFlags=i.subtreeFlags&14680064),l!==null?o=jt(l,o):(o=sn(o,s,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,s=e.child.memoizedState,s=s===null?al(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},o.memoizedState=s,o.childLanes=e.childLanes&~n,t.memoizedState=ll,r}return o=e.child,e=o.sibling,r=jt(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function ma(e,t){return t=Lo({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function gi(e,t,n,r){return r!==null&&ta(r),Hn(t,e.child,null,n),e=ma(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Hg(e,t,n,r,i,o,s){if(n)return t.flags&256?(t.flags&=-257,r=us(Error(w(422))),gi(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=Lo({mode:"visible",children:r.children},i,0,null),o=sn(o,i,s,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&Hn(t,e.child,null,s),t.child.memoizedState=al(s),t.memoizedState=ll,o);if(!(t.mode&1))return gi(e,t,s,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var l=r.dgst;return r=l,o=Error(w(419)),r=us(o,r,void 0),gi(e,t,s,r)}if(l=(s&e.childLanes)!==0,Pe||l){if(r=se,r!==null){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|s)?0:i,i!==0&&i!==o.retryLane&&(o.retryLane=i,mt(e,i),Ze(r,e,i,-1))}return wa(),r=us(Error(w(421))),gi(e,t,s,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=ry.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,De=Rt(i.nextSibling),Me=t,H=!0,Xe=null,e!==null&&(Ie[Be++]=lt,Ie[Be++]=at,Ie[Be++]=un,lt=e.id,at=e.overflow,un=t),t=ma(t,r.children),t.flags|=4096,t)}function Gu(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),tl(e.return,t,n)}function cs(e,t,n,r,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function qd(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(ve(e,t,r.children,n),r=G.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Gu(e,n,t);else if(e.tag===19)Gu(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(I(G,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&eo(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),cs(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&eo(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}cs(t,!0,n,null,o);break;case"together":cs(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ri(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function gt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),fn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(w(153));if(t.child!==null){for(e=t.child,n=jt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=jt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Gg(e,t,n){switch(t.tag){case 3:Zd(t),Wn();break;case 5:Cd(t);break;case 1:Te(t.type)&&Xi(t);break;case 4:la(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;I(Ji,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(I(G,G.current&1),t.flags|=128,null):n&t.child.childLanes?Jd(e,t,n):(I(G,G.current&1),e=gt(e,t,n),e!==null?e.sibling:null);I(G,G.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return qd(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),I(G,G.current),r)break;return null;case 22:case 23:return t.lanes=0,Xd(e,t,n)}return gt(e,t,n)}var bd,ul,ep,tp;bd=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ul=function(){};ep=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,nn(rt.current);var o=null;switch(n){case"input":i=Ms(e,i),r=Ms(e,r),o=[];break;case"select":i=X({},i,{value:void 0}),r=X({},r,{value:void 0}),o=[];break;case"textarea":i=Ns(e,i),r=Ns(e,r),o=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Ki)}js(n,r);var s;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var l=i[u];for(s in l)l.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Vr.hasOwnProperty(u)?o||(o=[]):(o=o||[]).push(u,null));for(u in r){var a=r[u];if(l=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(s in l)!l.hasOwnProperty(s)||a&&a.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in a)a.hasOwnProperty(s)&&l[s]!==a[s]&&(n||(n={}),n[s]=a[s])}else n||(o||(o=[]),o.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(o=o||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(o=o||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Vr.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&B("scroll",e),o||l===a||(o=[])):(o=o||[]).push(u,a))}n&&(o=o||[]).push("style",n);var u=o;(t.updateQueue=u)&&(t.flags|=4)}};tp=function(e,t,n,r){n!==r&&(t.flags|=4)};function or(e,t){if(!H)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function de(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Kg(e,t,n){var r=t.pendingProps;switch(ea(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return de(t),null;case 1:return Te(t.type)&&Qi(),de(t),null;case 3:return r=t.stateNode,Gn(),U(Ce),U(me),ua(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(hi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Xe!==null&&(yl(Xe),Xe=null))),ul(e,t),de(t),null;case 5:aa(t);var i=nn(Ir.current);if(n=t.type,e!==null&&t.stateNode!=null)ep(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(w(166));return de(t),null}if(e=nn(rt.current),hi(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[tt]=t,r[Or]=o,e=(t.mode&1)!==0,n){case"dialog":B("cancel",r),B("close",r);break;case"iframe":case"object":case"embed":B("load",r);break;case"video":case"audio":for(i=0;i<dr.length;i++)B(dr[i],r);break;case"source":B("error",r);break;case"img":case"image":case"link":B("error",r),B("load",r);break;case"details":B("toggle",r);break;case"input":eu(r,o),B("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},B("invalid",r);break;case"textarea":nu(r,o),B("invalid",r)}js(n,o),i=null;for(var s in o)if(o.hasOwnProperty(s)){var l=o[s];s==="children"?typeof l=="string"?r.textContent!==l&&(o.suppressHydrationWarning!==!0&&pi(r.textContent,l,e),i=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(o.suppressHydrationWarning!==!0&&pi(r.textContent,l,e),i=["children",""+l]):Vr.hasOwnProperty(s)&&l!=null&&s==="onScroll"&&B("scroll",r)}switch(n){case"input":oi(r),tu(r,o,!0);break;case"textarea":oi(r),ru(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=Ki)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Af(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[tt]=t,e[Or]=r,bd(e,t,!1,!1),t.stateNode=e;e:{switch(s=Os(n,r),n){case"dialog":B("cancel",e),B("close",e),i=r;break;case"iframe":case"object":case"embed":B("load",e),i=r;break;case"video":case"audio":for(i=0;i<dr.length;i++)B(dr[i],e);i=r;break;case"source":B("error",e),i=r;break;case"img":case"image":case"link":B("error",e),B("load",e),i=r;break;case"details":B("toggle",e),i=r;break;case"input":eu(e,r),i=Ms(e,r),B("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=X({},r,{value:void 0}),B("invalid",e);break;case"textarea":nu(e,r),i=Ns(e,r),B("invalid",e);break;default:i=r}js(n,i),l=i;for(o in l)if(l.hasOwnProperty(o)){var a=l[o];o==="style"?Rf(e,a):o==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&Df(e,a)):o==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&Lr(e,a):typeof a=="number"&&Lr(e,""+a):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(Vr.hasOwnProperty(o)?a!=null&&o==="onScroll"&&B("scroll",e):a!=null&&Il(e,o,a,s))}switch(n){case"input":oi(e),tu(e,r,!1);break;case"textarea":oi(e),ru(e);break;case"option":r.value!=null&&e.setAttribute("value",""+zt(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?Fn(e,!!r.multiple,o,!1):r.defaultValue!=null&&Fn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=Ki)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return de(t),null;case 6:if(e&&t.stateNode!=null)tp(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(w(166));if(n=nn(Ir.current),nn(rt.current),hi(t)){if(r=t.stateNode,n=t.memoizedProps,r[tt]=t,(o=r.nodeValue!==n)&&(e=Me,e!==null))switch(e.tag){case 3:pi(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&pi(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[tt]=t,t.stateNode=r}return de(t),null;case 13:if(U(G),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(H&&De!==null&&t.mode&1&&!(t.flags&128))xd(),Wn(),t.flags|=98560,o=!1;else if(o=hi(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(w(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(w(317));o[tt]=t}else Wn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;de(t),o=!1}else Xe!==null&&(yl(Xe),Xe=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||G.current&1?ne===0&&(ne=3):wa())),t.updateQueue!==null&&(t.flags|=4),de(t),null);case 4:return Gn(),ul(e,t),e===null&&Fr(t.stateNode.containerInfo),de(t),null;case 10:return ia(t.type._context),de(t),null;case 17:return Te(t.type)&&Qi(),de(t),null;case 19:if(U(G),o=t.memoizedState,o===null)return de(t),null;if(r=(t.flags&128)!==0,s=o.rendering,s===null)if(r)or(o,!1);else{if(ne!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=eo(e),s!==null){for(t.flags|=128,or(o,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,s=o.alternate,s===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return I(G,G.current&1|2),t.child}e=e.sibling}o.tail!==null&&J()>Qn&&(t.flags|=128,r=!0,or(o,!1),t.lanes=4194304)}else{if(!r)if(e=eo(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),or(o,!0),o.tail===null&&o.tailMode==="hidden"&&!s.alternate&&!H)return de(t),null}else 2*J()-o.renderingStartTime>Qn&&n!==1073741824&&(t.flags|=128,r=!0,or(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(n=o.last,n!==null?n.sibling=s:t.child=s,o.last=s)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=J(),t.sibling=null,n=G.current,I(G,r?n&1|2:n&1),t):(de(t),null);case 22:case 23:return Sa(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ae&1073741824&&(de(t),t.subtreeFlags&6&&(t.flags|=8192)):de(t),null;case 24:return null;case 25:return null}throw Error(w(156,t.tag))}function Qg(e,t){switch(ea(t),t.tag){case 1:return Te(t.type)&&Qi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Gn(),U(Ce),U(me),ua(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return aa(t),null;case 13:if(U(G),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(w(340));Wn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return U(G),null;case 4:return Gn(),null;case 10:return ia(t.type._context),null;case 22:case 23:return Sa(),null;case 24:return null;default:return null}}var yi=!1,he=!1,Xg=typeof WeakSet=="function"?WeakSet:Set,V=null;function An(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Y(e,t,r)}else n.current=null}function cl(e,t,n){try{n()}catch(r){Y(e,t,r)}}var Ku=!1;function Yg(e,t){if(Qs=Wi,e=sd(),ql(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var s=0,l=-1,a=-1,u=0,c=0,f=e,d=null;t:for(;;){for(var g;f!==n||i!==0&&f.nodeType!==3||(l=s+i),f!==o||r!==0&&f.nodeType!==3||(a=s+r),f.nodeType===3&&(s+=f.nodeValue.length),(g=f.firstChild)!==null;)d=f,f=g;for(;;){if(f===e)break t;if(d===n&&++u===i&&(l=s),d===o&&++c===r&&(a=s),(g=f.nextSibling)!==null)break;f=d,d=f.parentNode}f=g}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(Xs={focusedElem:e,selectionRange:n},Wi=!1,V=t;V!==null;)if(t=V,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,V=e;else for(;V!==null;){t=V;try{var y=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(y!==null){var v=y.memoizedProps,k=y.memoizedState,m=t.stateNode,p=m.getSnapshotBeforeUpdate(t.elementType===t.type?v:Ke(t.type,v),k);m.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var h=t.stateNode.containerInfo;h.nodeType===1?h.textContent="":h.nodeType===9&&h.documentElement&&h.removeChild(h.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(w(163))}}catch(x){Y(t,t.return,x)}if(e=t.sibling,e!==null){e.return=t.return,V=e;break}V=t.return}return y=Ku,Ku=!1,y}function Sr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,o!==void 0&&cl(t,n,o)}i=i.next}while(i!==r)}}function Eo(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function fl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function np(e){var t=e.alternate;t!==null&&(e.alternate=null,np(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[tt],delete t[Or],delete t[Js],delete t[Mg],delete t[Rg])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function rp(e){return e.tag===5||e.tag===3||e.tag===4}function Qu(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||rp(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function dl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Ki));else if(r!==4&&(e=e.child,e!==null))for(dl(e,t,n),e=e.sibling;e!==null;)dl(e,t,n),e=e.sibling}function pl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(pl(e,t,n),e=e.sibling;e!==null;)pl(e,t,n),e=e.sibling}var ae=null,Qe=!1;function St(e,t,n){for(n=n.child;n!==null;)ip(e,t,n),n=n.sibling}function ip(e,t,n){if(nt&&typeof nt.onCommitFiberUnmount=="function")try{nt.onCommitFiberUnmount(vo,n)}catch{}switch(n.tag){case 5:he||An(n,t);case 6:var r=ae,i=Qe;ae=null,St(e,t,n),ae=r,Qe=i,ae!==null&&(Qe?(e=ae,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ae.removeChild(n.stateNode));break;case 18:ae!==null&&(Qe?(e=ae,n=n.stateNode,e.nodeType===8?rs(e.parentNode,n):e.nodeType===1&&rs(e,n),Rr(e)):rs(ae,n.stateNode));break;case 4:r=ae,i=Qe,ae=n.stateNode.containerInfo,Qe=!0,St(e,t,n),ae=r,Qe=i;break;case 0:case 11:case 14:case 15:if(!he&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var o=i,s=o.destroy;o=o.tag,s!==void 0&&(o&2||o&4)&&cl(n,t,s),i=i.next}while(i!==r)}St(e,t,n);break;case 1:if(!he&&(An(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){Y(n,t,l)}St(e,t,n);break;case 21:St(e,t,n);break;case 22:n.mode&1?(he=(r=he)||n.memoizedState!==null,St(e,t,n),he=r):St(e,t,n);break;default:St(e,t,n)}}function Xu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Xg),t.forEach(function(r){var i=iy.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function Ge(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,s=t,l=s;e:for(;l!==null;){switch(l.tag){case 5:ae=l.stateNode,Qe=!1;break e;case 3:ae=l.stateNode.containerInfo,Qe=!0;break e;case 4:ae=l.stateNode.containerInfo,Qe=!0;break e}l=l.return}if(ae===null)throw Error(w(160));ip(o,s,i),ae=null,Qe=!1;var a=i.alternate;a!==null&&(a.return=null),i.return=null}catch(u){Y(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)op(t,e),t=t.sibling}function op(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ge(t,e),be(e),r&4){try{Sr(3,e,e.return),Eo(3,e)}catch(v){Y(e,e.return,v)}try{Sr(5,e,e.return)}catch(v){Y(e,e.return,v)}}break;case 1:Ge(t,e),be(e),r&512&&n!==null&&An(n,n.return);break;case 5:if(Ge(t,e),be(e),r&512&&n!==null&&An(n,n.return),e.flags&32){var i=e.stateNode;try{Lr(i,"")}catch(v){Y(e,e.return,v)}}if(r&4&&(i=e.stateNode,i!=null)){var o=e.memoizedProps,s=n!==null?n.memoizedProps:o,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&o.type==="radio"&&o.name!=null&&Vf(i,o),Os(l,s);var u=Os(l,o);for(s=0;s<a.length;s+=2){var c=a[s],f=a[s+1];c==="style"?Rf(i,f):c==="dangerouslySetInnerHTML"?Df(i,f):c==="children"?Lr(i,f):Il(i,c,f,u)}switch(l){case"input":Rs(i,o);break;case"textarea":Lf(i,o);break;case"select":var d=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var g=o.value;g!=null?Fn(i,!!o.multiple,g,!1):d!==!!o.multiple&&(o.defaultValue!=null?Fn(i,!!o.multiple,o.defaultValue,!0):Fn(i,!!o.multiple,o.multiple?[]:"",!1))}i[Or]=o}catch(v){Y(e,e.return,v)}}break;case 6:if(Ge(t,e),be(e),r&4){if(e.stateNode===null)throw Error(w(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(v){Y(e,e.return,v)}}break;case 3:if(Ge(t,e),be(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Rr(t.containerInfo)}catch(v){Y(e,e.return,v)}break;case 4:Ge(t,e),be(e);break;case 13:Ge(t,e),be(e),i=e.child,i.flags&8192&&(o=i.memoizedState!==null,i.stateNode.isHidden=o,!o||i.alternate!==null&&i.alternate.memoizedState!==null||(va=J())),r&4&&Xu(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(he=(u=he)||c,Ge(t,e),he=u):Ge(t,e),be(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(V=e,c=e.child;c!==null;){for(f=V=c;V!==null;){switch(d=V,g=d.child,d.tag){case 0:case 11:case 14:case 15:Sr(4,d,d.return);break;case 1:An(d,d.return);var y=d.stateNode;if(typeof y.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,y.props=t.memoizedProps,y.state=t.memoizedState,y.componentWillUnmount()}catch(v){Y(r,n,v)}}break;case 5:An(d,d.return);break;case 22:if(d.memoizedState!==null){Zu(f);continue}}g!==null?(g.return=d,V=g):Zu(f)}c=c.sibling}e:for(c=null,f=e;;){if(f.tag===5){if(c===null){c=f;try{i=f.stateNode,u?(o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(l=f.stateNode,a=f.memoizedProps.style,s=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=Mf("display",s))}catch(v){Y(e,e.return,v)}}}else if(f.tag===6){if(c===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(v){Y(e,e.return,v)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;c===f&&(c=null),f=f.return}c===f&&(c=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:Ge(t,e),be(e),r&4&&Xu(e);break;case 21:break;default:Ge(t,e),be(e)}}function be(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(rp(n)){var r=n;break e}n=n.return}throw Error(w(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Lr(i,""),r.flags&=-33);var o=Qu(e);pl(e,o,i);break;case 3:case 4:var s=r.stateNode.containerInfo,l=Qu(e);dl(e,l,s);break;default:throw Error(w(161))}}catch(a){Y(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Zg(e,t,n){V=e,sp(e)}function sp(e,t,n){for(var r=(e.mode&1)!==0;V!==null;){var i=V,o=i.child;if(i.tag===22&&r){var s=i.memoizedState!==null||yi;if(!s){var l=i.alternate,a=l!==null&&l.memoizedState!==null||he;l=yi;var u=he;if(yi=s,(he=a)&&!u)for(V=i;V!==null;)s=V,a=s.child,s.tag===22&&s.memoizedState!==null?Ju(i):a!==null?(a.return=s,V=a):Ju(i);for(;o!==null;)V=o,sp(o),o=o.sibling;V=i,yi=l,he=u}Yu(e)}else i.subtreeFlags&8772&&o!==null?(o.return=i,V=o):Yu(e)}}function Yu(e){for(;V!==null;){var t=V;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:he||Eo(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!he)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:Ke(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&_u(t,o,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}_u(t,s,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var f=c.dehydrated;f!==null&&Rr(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(w(163))}he||t.flags&512&&fl(t)}catch(d){Y(t,t.return,d)}}if(t===e){V=null;break}if(n=t.sibling,n!==null){n.return=t.return,V=n;break}V=t.return}}function Zu(e){for(;V!==null;){var t=V;if(t===e){V=null;break}var n=t.sibling;if(n!==null){n.return=t.return,V=n;break}V=t.return}}function Ju(e){for(;V!==null;){var t=V;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Eo(4,t)}catch(a){Y(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(a){Y(t,i,a)}}var o=t.return;try{fl(t)}catch(a){Y(t,o,a)}break;case 5:var s=t.return;try{fl(t)}catch(a){Y(t,s,a)}}}catch(a){Y(t,t.return,a)}if(t===e){V=null;break}var l=t.sibling;if(l!==null){l.return=t.return,V=l;break}V=t.return}}var Jg=Math.ceil,ro=vt.ReactCurrentDispatcher,ga=vt.ReactCurrentOwner,$e=vt.ReactCurrentBatchConfig,j=0,se=null,ee=null,ue=0,Ae=0,Dn=Wt(0),ne=0,Wr=null,fn=0,Vo=0,ya=0,wr=null,ke=null,va=0,Qn=1/0,ot=null,io=!1,hl=null,Nt=null,vi=!1,Lt=null,oo=0,kr=0,ml=null,_i=-1,Ni=0;function xe(){return j&6?J():_i!==-1?_i:_i=J()}function Ft(e){return e.mode&1?j&2&&ue!==0?ue&-ue:Ng.transition!==null?(Ni===0&&(Ni=Hf()),Ni):(e=z,e!==0||(e=window.event,e=e===void 0?16:Jf(e.type)),e):1}function Ze(e,t,n,r){if(50<kr)throw kr=0,ml=null,Error(w(185));Xr(e,n,r),(!(j&2)||e!==se)&&(e===se&&(!(j&2)&&(Vo|=n),ne===4&&Et(e,ue)),Ee(e,r),n===1&&j===0&&!(t.mode&1)&&(Qn=J()+500,Po&&Ht()))}function Ee(e,t){var n=e.callbackNode;Nm(e,t);var r=$i(e,e===se?ue:0);if(r===0)n!==null&&su(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&su(n),t===1)e.tag===0?_g(qu.bind(null,e)):gd(qu.bind(null,e)),Ag(function(){!(j&6)&&Ht()}),n=null;else{switch(Gf(r)){case 1:n=Hl;break;case 4:n=$f;break;case 16:n=Ui;break;case 536870912:n=Wf;break;default:n=Ui}n=hp(n,lp.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function lp(e,t){if(_i=-1,Ni=0,j&6)throw Error(w(327));var n=e.callbackNode;if(Bn()&&e.callbackNode!==n)return null;var r=$i(e,e===se?ue:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=so(e,r);else{t=r;var i=j;j|=2;var o=up();(se!==e||ue!==t)&&(ot=null,Qn=J()+500,on(e,t));do try{ey();break}catch(l){ap(e,l)}while(!0);ra(),ro.current=o,j=i,ee!==null?t=0:(se=null,ue=0,t=ne)}if(t!==0){if(t===2&&(i=$s(e),i!==0&&(r=i,t=gl(e,i))),t===1)throw n=Wr,on(e,0),Et(e,r),Ee(e,J()),n;if(t===6)Et(e,r);else{if(i=e.current.alternate,!(r&30)&&!qg(i)&&(t=so(e,r),t===2&&(o=$s(e),o!==0&&(r=o,t=gl(e,o))),t===1))throw n=Wr,on(e,0),Et(e,r),Ee(e,J()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(w(345));case 2:Jt(e,ke,ot);break;case 3:if(Et(e,r),(r&130023424)===r&&(t=va+500-J(),10<t)){if($i(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){xe(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Zs(Jt.bind(null,e,ke,ot),t);break}Jt(e,ke,ot);break;case 4:if(Et(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-Ye(r);o=1<<s,s=t[s],s>i&&(i=s),r&=~o}if(r=i,r=J()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Jg(r/1960))-r,10<r){e.timeoutHandle=Zs(Jt.bind(null,e,ke,ot),r);break}Jt(e,ke,ot);break;case 5:Jt(e,ke,ot);break;default:throw Error(w(329))}}}return Ee(e,J()),e.callbackNode===n?lp.bind(null,e):null}function gl(e,t){var n=wr;return e.current.memoizedState.isDehydrated&&(on(e,t).flags|=256),e=so(e,t),e!==2&&(t=ke,ke=n,t!==null&&yl(t)),e}function yl(e){ke===null?ke=e:ke.push.apply(ke,e)}function qg(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!Je(o(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Et(e,t){for(t&=~ya,t&=~Vo,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ye(t),r=1<<n;e[n]=-1,t&=~r}}function qu(e){if(j&6)throw Error(w(327));Bn();var t=$i(e,0);if(!(t&1))return Ee(e,J()),null;var n=so(e,t);if(e.tag!==0&&n===2){var r=$s(e);r!==0&&(t=r,n=gl(e,r))}if(n===1)throw n=Wr,on(e,0),Et(e,t),Ee(e,J()),n;if(n===6)throw Error(w(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Jt(e,ke,ot),Ee(e,J()),null}function xa(e,t){var n=j;j|=1;try{return e(t)}finally{j=n,j===0&&(Qn=J()+500,Po&&Ht())}}function dn(e){Lt!==null&&Lt.tag===0&&!(j&6)&&Bn();var t=j;j|=1;var n=$e.transition,r=z;try{if($e.transition=null,z=1,e)return e()}finally{z=r,$e.transition=n,j=t,!(j&6)&&Ht()}}function Sa(){Ae=Dn.current,U(Dn)}function on(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Lg(n)),ee!==null)for(n=ee.return;n!==null;){var r=n;switch(ea(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Qi();break;case 3:Gn(),U(Ce),U(me),ua();break;case 5:aa(r);break;case 4:Gn();break;case 13:U(G);break;case 19:U(G);break;case 10:ia(r.type._context);break;case 22:case 23:Sa()}n=n.return}if(se=e,ee=e=jt(e.current,null),ue=Ae=t,ne=0,Wr=null,ya=Vo=fn=0,ke=wr=null,tn!==null){for(t=0;t<tn.length;t++)if(n=tn[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,o=n.pending;if(o!==null){var s=o.next;o.next=i,r.next=s}n.pending=r}tn=null}return e}function ap(e,t){do{var n=ee;try{if(ra(),Di.current=no,to){for(var r=Q.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}to=!1}if(cn=0,oe=te=Q=null,xr=!1,Br=0,ga.current=null,n===null||n.return===null){ne=1,Wr=t,ee=null;break}e:{var o=e,s=n.return,l=n,a=t;if(t=ue,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,c=l,f=c.tag;if(!(c.mode&1)&&(f===0||f===11||f===15)){var d=c.alternate;d?(c.updateQueue=d.updateQueue,c.memoizedState=d.memoizedState,c.lanes=d.lanes):(c.updateQueue=null,c.memoizedState=null)}var g=Iu(s);if(g!==null){g.flags&=-257,Bu(g,s,l,o,t),g.mode&1&&zu(o,u,t),t=g,a=u;var y=t.updateQueue;if(y===null){var v=new Set;v.add(a),t.updateQueue=v}else y.add(a);break e}else{if(!(t&1)){zu(o,u,t),wa();break e}a=Error(w(426))}}else if(H&&l.mode&1){var k=Iu(s);if(k!==null){!(k.flags&65536)&&(k.flags|=256),Bu(k,s,l,o,t),ta(Kn(a,l));break e}}o=a=Kn(a,l),ne!==4&&(ne=2),wr===null?wr=[o]:wr.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var m=Gd(o,a,t);Ru(o,m);break e;case 1:l=a;var p=o.type,h=o.stateNode;if(!(o.flags&128)&&(typeof p.getDerivedStateFromError=="function"||h!==null&&typeof h.componentDidCatch=="function"&&(Nt===null||!Nt.has(h)))){o.flags|=65536,t&=-t,o.lanes|=t;var x=Kd(o,l,t);Ru(o,x);break e}}o=o.return}while(o!==null)}fp(n)}catch(S){t=S,ee===n&&n!==null&&(ee=n=n.return);continue}break}while(!0)}function up(){var e=ro.current;return ro.current=no,e===null?no:e}function wa(){(ne===0||ne===3||ne===2)&&(ne=4),se===null||!(fn&268435455)&&!(Vo&268435455)||Et(se,ue)}function so(e,t){var n=j;j|=2;var r=up();(se!==e||ue!==t)&&(ot=null,on(e,t));do try{bg();break}catch(i){ap(e,i)}while(!0);if(ra(),j=n,ro.current=r,ee!==null)throw Error(w(261));return se=null,ue=0,ne}function bg(){for(;ee!==null;)cp(ee)}function ey(){for(;ee!==null&&!Tm();)cp(ee)}function cp(e){var t=pp(e.alternate,e,Ae);e.memoizedProps=e.pendingProps,t===null?fp(e):ee=t,ga.current=null}function fp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Qg(n,t),n!==null){n.flags&=32767,ee=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ne=6,ee=null;return}}else if(n=Kg(n,t,Ae),n!==null){ee=n;return}if(t=t.sibling,t!==null){ee=t;return}ee=t=e}while(t!==null);ne===0&&(ne=5)}function Jt(e,t,n){var r=z,i=$e.transition;try{$e.transition=null,z=1,ty(e,t,n,r)}finally{$e.transition=i,z=r}return null}function ty(e,t,n,r){do Bn();while(Lt!==null);if(j&6)throw Error(w(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(w(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(Fm(e,o),e===se&&(ee=se=null,ue=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||vi||(vi=!0,hp(Ui,function(){return Bn(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=$e.transition,$e.transition=null;var s=z;z=1;var l=j;j|=4,ga.current=null,Yg(e,n),op(n,e),wg(Xs),Wi=!!Qs,Xs=Qs=null,e.current=n,Zg(n),Em(),j=l,z=s,$e.transition=o}else e.current=n;if(vi&&(vi=!1,Lt=e,oo=i),o=e.pendingLanes,o===0&&(Nt=null),Am(n.stateNode),Ee(e,J()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(io)throw io=!1,e=hl,hl=null,e;return oo&1&&e.tag!==0&&Bn(),o=e.pendingLanes,o&1?e===ml?kr++:(kr=0,ml=e):kr=0,Ht(),null}function Bn(){if(Lt!==null){var e=Gf(oo),t=$e.transition,n=z;try{if($e.transition=null,z=16>e?16:e,Lt===null)var r=!1;else{if(e=Lt,Lt=null,oo=0,j&6)throw Error(w(331));var i=j;for(j|=4,V=e.current;V!==null;){var o=V,s=o.child;if(V.flags&16){var l=o.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(V=u;V!==null;){var c=V;switch(c.tag){case 0:case 11:case 15:Sr(8,c,o)}var f=c.child;if(f!==null)f.return=c,V=f;else for(;V!==null;){c=V;var d=c.sibling,g=c.return;if(np(c),c===u){V=null;break}if(d!==null){d.return=g,V=d;break}V=g}}}var y=o.alternate;if(y!==null){var v=y.child;if(v!==null){y.child=null;do{var k=v.sibling;v.sibling=null,v=k}while(v!==null)}}V=o}}if(o.subtreeFlags&2064&&s!==null)s.return=o,V=s;else e:for(;V!==null;){if(o=V,o.flags&2048)switch(o.tag){case 0:case 11:case 15:Sr(9,o,o.return)}var m=o.sibling;if(m!==null){m.return=o.return,V=m;break e}V=o.return}}var p=e.current;for(V=p;V!==null;){s=V;var h=s.child;if(s.subtreeFlags&2064&&h!==null)h.return=s,V=h;else e:for(s=p;V!==null;){if(l=V,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:Eo(9,l)}}catch(S){Y(l,l.return,S)}if(l===s){V=null;break e}var x=l.sibling;if(x!==null){x.return=l.return,V=x;break e}V=l.return}}if(j=i,Ht(),nt&&typeof nt.onPostCommitFiberRoot=="function")try{nt.onPostCommitFiberRoot(vo,e)}catch{}r=!0}return r}finally{z=n,$e.transition=t}}return!1}function bu(e,t,n){t=Kn(n,t),t=Gd(e,t,1),e=_t(e,t,1),t=xe(),e!==null&&(Xr(e,1,t),Ee(e,t))}function Y(e,t,n){if(e.tag===3)bu(e,e,n);else for(;t!==null;){if(t.tag===3){bu(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Nt===null||!Nt.has(r))){e=Kn(n,e),e=Kd(t,e,1),t=_t(t,e,1),e=xe(),t!==null&&(Xr(t,1,e),Ee(t,e));break}}t=t.return}}function ny(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=xe(),e.pingedLanes|=e.suspendedLanes&n,se===e&&(ue&n)===n&&(ne===4||ne===3&&(ue&130023424)===ue&&500>J()-va?on(e,0):ya|=n),Ee(e,t)}function dp(e,t){t===0&&(e.mode&1?(t=ai,ai<<=1,!(ai&130023424)&&(ai=4194304)):t=1);var n=xe();e=mt(e,t),e!==null&&(Xr(e,t,n),Ee(e,n))}function ry(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),dp(e,n)}function iy(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(w(314))}r!==null&&r.delete(t),dp(e,n)}var pp;pp=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ce.current)Pe=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Pe=!1,Gg(e,t,n);Pe=!!(e.flags&131072)}else Pe=!1,H&&t.flags&1048576&&yd(t,Zi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Ri(e,t),e=t.pendingProps;var i=$n(t,me.current);In(t,n),i=fa(null,t,r,e,i,n);var o=da();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Te(r)?(o=!0,Xi(t)):o=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,sa(t),i.updater=To,t.stateNode=i,i._reactInternals=t,rl(t,r,e,n),t=sl(null,t,r,!0,o,n)):(t.tag=0,H&&o&&bl(t),ve(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Ri(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=sy(r),e=Ke(r,e),i){case 0:t=ol(null,t,r,e,n);break e;case 1:t=Wu(null,t,r,e,n);break e;case 11:t=Uu(null,t,r,e,n);break e;case 14:t=$u(null,t,r,Ke(r.type,e),n);break e}throw Error(w(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ke(r,i),ol(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ke(r,i),Wu(e,t,r,i,n);case 3:e:{if(Zd(t),e===null)throw Error(w(387));r=t.pendingProps,o=t.memoizedState,i=o.element,Pd(e,t),bi(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){i=Kn(Error(w(423)),t),t=Hu(e,t,r,n,i);break e}else if(r!==i){i=Kn(Error(w(424)),t),t=Hu(e,t,r,n,i);break e}else for(De=Rt(t.stateNode.containerInfo.firstChild),Me=t,H=!0,Xe=null,n=wd(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Wn(),r===i){t=gt(e,t,n);break e}ve(e,t,r,n)}t=t.child}return t;case 5:return Cd(t),e===null&&el(t),r=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,s=i.children,Ys(r,i)?s=null:o!==null&&Ys(r,o)&&(t.flags|=32),Yd(e,t),ve(e,t,s,n),t.child;case 6:return e===null&&el(t),null;case 13:return Jd(e,t,n);case 4:return la(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Hn(t,null,r,n):ve(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ke(r,i),Uu(e,t,r,i,n);case 7:return ve(e,t,t.pendingProps,n),t.child;case 8:return ve(e,t,t.pendingProps.children,n),t.child;case 12:return ve(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,s=i.value,I(Ji,r._currentValue),r._currentValue=s,o!==null)if(Je(o.value,s)){if(o.children===i.children&&!Ce.current){t=gt(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var l=o.dependencies;if(l!==null){s=o.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(o.tag===1){a=ct(-1,n&-n),a.tag=2;var u=o.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?a.next=a:(a.next=c.next,c.next=a),u.pending=a}}o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),tl(o.return,n,t),l.lanes|=n;break}a=a.next}}else if(o.tag===10)s=o.type===t.type?null:o.child;else if(o.tag===18){if(s=o.return,s===null)throw Error(w(341));s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),tl(s,n,t),s=o.sibling}else s=o.child;if(s!==null)s.return=o;else for(s=o;s!==null;){if(s===t){s=null;break}if(o=s.sibling,o!==null){o.return=s.return,s=o;break}s=s.return}o=s}ve(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,In(t,n),i=We(i),r=r(i),t.flags|=1,ve(e,t,r,n),t.child;case 14:return r=t.type,i=Ke(r,t.pendingProps),i=Ke(r.type,i),$u(e,t,r,i,n);case 15:return Qd(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ke(r,i),Ri(e,t),t.tag=1,Te(r)?(e=!0,Xi(t)):e=!1,In(t,n),Hd(t,r,i),rl(t,r,i,n),sl(null,t,r,!0,e,n);case 19:return qd(e,t,n);case 22:return Xd(e,t,n)}throw Error(w(156,t.tag))};function hp(e,t){return Uf(e,t)}function oy(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ue(e,t,n,r){return new oy(e,t,n,r)}function ka(e){return e=e.prototype,!(!e||!e.isReactComponent)}function sy(e){if(typeof e=="function")return ka(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Ul)return 11;if(e===$l)return 14}return 2}function jt(e,t){var n=e.alternate;return n===null?(n=Ue(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Fi(e,t,n,r,i,o){var s=2;if(r=e,typeof e=="function")ka(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case Sn:return sn(n.children,i,o,t);case Bl:s=8,i|=8;break;case Vs:return e=Ue(12,n,t,i|2),e.elementType=Vs,e.lanes=o,e;case Ls:return e=Ue(13,n,t,i),e.elementType=Ls,e.lanes=o,e;case As:return e=Ue(19,n,t,i),e.elementType=As,e.lanes=o,e;case Cf:return Lo(n,i,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case kf:s=10;break e;case Pf:s=9;break e;case Ul:s=11;break e;case $l:s=14;break e;case kt:s=16,r=null;break e}throw Error(w(130,e==null?e:typeof e,""))}return t=Ue(s,n,t,i),t.elementType=e,t.type=r,t.lanes=o,t}function sn(e,t,n,r){return e=Ue(7,e,r,t),e.lanes=n,e}function Lo(e,t,n,r){return e=Ue(22,e,r,t),e.elementType=Cf,e.lanes=n,e.stateNode={isHidden:!1},e}function fs(e,t,n){return e=Ue(6,e,null,t),e.lanes=n,e}function ds(e,t,n){return t=Ue(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function ly(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ko(0),this.expirationTimes=Ko(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ko(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function Pa(e,t,n,r,i,o,s,l,a){return e=new ly(e,t,n,l,a),t===1?(t=1,o===!0&&(t|=8)):t=0,o=Ue(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},sa(o),e}function ay(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:xn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function mp(e){if(!e)return It;e=e._reactInternals;e:{if(hn(e)!==e||e.tag!==1)throw Error(w(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Te(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(w(171))}if(e.tag===1){var n=e.type;if(Te(n))return md(e,n,t)}return t}function gp(e,t,n,r,i,o,s,l,a){return e=Pa(n,r,!0,e,i,o,s,l,a),e.context=mp(null),n=e.current,r=xe(),i=Ft(n),o=ct(r,i),o.callback=t??null,_t(n,o,i),e.current.lanes=i,Xr(e,i,r),Ee(e,r),e}function Ao(e,t,n,r){var i=t.current,o=xe(),s=Ft(i);return n=mp(n),t.context===null?t.context=n:t.pendingContext=n,t=ct(o,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=_t(i,t,s),e!==null&&(Ze(e,i,s,o),Ai(e,i,s)),s}function lo(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function ec(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Ca(e,t){ec(e,t),(e=e.alternate)&&ec(e,t)}function uy(){return null}var yp=typeof reportError=="function"?reportError:function(e){console.error(e)};function Ta(e){this._internalRoot=e}Do.prototype.render=Ta.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(w(409));Ao(e,t,null,null)};Do.prototype.unmount=Ta.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;dn(function(){Ao(null,e,null,null)}),t[ht]=null}};function Do(e){this._internalRoot=e}Do.prototype.unstable_scheduleHydration=function(e){if(e){var t=Xf();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Tt.length&&t!==0&&t<Tt[n].priority;n++);Tt.splice(n,0,e),n===0&&Zf(e)}};function Ea(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Mo(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function tc(){}function cy(e,t,n,r,i){if(i){if(typeof r=="function"){var o=r;r=function(){var u=lo(s);o.call(u)}}var s=gp(t,r,e,0,null,!1,!1,"",tc);return e._reactRootContainer=s,e[ht]=s.current,Fr(e.nodeType===8?e.parentNode:e),dn(),s}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var l=r;r=function(){var u=lo(a);l.call(u)}}var a=Pa(e,0,!1,null,null,!1,!1,"",tc);return e._reactRootContainer=a,e[ht]=a.current,Fr(e.nodeType===8?e.parentNode:e),dn(function(){Ao(t,a,n,r)}),a}function Ro(e,t,n,r,i){var o=n._reactRootContainer;if(o){var s=o;if(typeof i=="function"){var l=i;i=function(){var a=lo(s);l.call(a)}}Ao(t,s,e,i)}else s=cy(n,t,e,i,r);return lo(s)}Kf=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=fr(t.pendingLanes);n!==0&&(Gl(t,n|1),Ee(t,J()),!(j&6)&&(Qn=J()+500,Ht()))}break;case 13:dn(function(){var r=mt(e,1);if(r!==null){var i=xe();Ze(r,e,1,i)}}),Ca(e,1)}};Kl=function(e){if(e.tag===13){var t=mt(e,134217728);if(t!==null){var n=xe();Ze(t,e,134217728,n)}Ca(e,134217728)}};Qf=function(e){if(e.tag===13){var t=Ft(e),n=mt(e,t);if(n!==null){var r=xe();Ze(n,e,t,r)}Ca(e,t)}};Xf=function(){return z};Yf=function(e,t){var n=z;try{return z=e,t()}finally{z=n}};Is=function(e,t,n){switch(t){case"input":if(Rs(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=ko(r);if(!i)throw Error(w(90));Ef(r),Rs(r,i)}}}break;case"textarea":Lf(e,n);break;case"select":t=n.value,t!=null&&Fn(e,!!n.multiple,t,!1)}};Ff=xa;jf=dn;var fy={usingClientEntryPoint:!1,Events:[Zr,Cn,ko,_f,Nf,xa]},sr={findFiberByHostInstance:en,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},dy={bundleType:sr.bundleType,version:sr.version,rendererPackageName:sr.rendererPackageName,rendererConfig:sr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:vt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=If(e),e===null?null:e.stateNode},findFiberByHostInstance:sr.findFiberByHostInstance||uy,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var xi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!xi.isDisabled&&xi.supportsFiber)try{vo=xi.inject(dy),nt=xi}catch{}}Ne.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=fy;Ne.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ea(t))throw Error(w(200));return ay(e,t,null,n)};Ne.createRoot=function(e,t){if(!Ea(e))throw Error(w(299));var n=!1,r="",i=yp;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=Pa(e,1,!1,null,null,n,!1,r,i),e[ht]=t.current,Fr(e.nodeType===8?e.parentNode:e),new Ta(t)};Ne.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(w(188)):(e=Object.keys(e).join(","),Error(w(268,e)));return e=If(t),e=e===null?null:e.stateNode,e};Ne.flushSync=function(e){return dn(e)};Ne.hydrate=function(e,t,n){if(!Mo(t))throw Error(w(200));return Ro(null,e,t,!0,n)};Ne.hydrateRoot=function(e,t,n){if(!Ea(e))throw Error(w(405));var r=n!=null&&n.hydratedSources||null,i=!1,o="",s=yp;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=gp(t,null,e,1,n??null,i,!1,o,s),e[ht]=t.current,Fr(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Do(t)};Ne.render=function(e,t,n){if(!Mo(t))throw Error(w(200));return Ro(null,e,t,!1,n)};Ne.unmountComponentAtNode=function(e){if(!Mo(e))throw Error(w(40));return e._reactRootContainer?(dn(function(){Ro(null,null,e,!1,function(){e._reactRootContainer=null,e[ht]=null})}),!0):!1};Ne.unstable_batchedUpdates=xa;Ne.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Mo(n))throw Error(w(200));if(e==null||e._reactInternals===void 0)throw Error(w(38));return Ro(e,t,n,!1,r)};Ne.version="18.3.1-next-f1338f8080-20240426";function vp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(vp)}catch(e){console.error(e)}}vp(),vf.exports=Ne;var py=vf.exports,nc=py;Ts.createRoot=nc.createRoot,Ts.hydrateRoot=nc.hydrateRoot;const xp=D.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),_o=D.createContext({}),Va=D.createContext(null),No=typeof document<"u",hy=No?D.useLayoutEffect:D.useEffect,Sp=D.createContext({strict:!1}),La=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),my="framerAppearId",wp="data-"+La(my);function gy(e,t,n,r){const{visualElement:i}=D.useContext(_o),o=D.useContext(Sp),s=D.useContext(Va),l=D.useContext(xp).reducedMotion,a=D.useRef();r=r||o.renderer,!a.current&&r&&(a.current=r(e,{visualState:t,parent:i,props:n,presenceContext:s,blockInitialAnimation:s?s.initial===!1:!1,reducedMotionConfig:l}));const u=a.current;D.useInsertionEffect(()=>{u&&u.update(n,s)});const c=D.useRef(!!(n[wp]&&!window.HandoffComplete));return hy(()=>{u&&(u.render(),c.current&&u.animationState&&u.animationState.animateChanges())}),D.useEffect(()=>{u&&(u.updateFeatures(),!c.current&&u.animationState&&u.animationState.animateChanges(),c.current&&(c.current=!1,window.HandoffComplete=!0))}),u}function Mn(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function yy(e,t,n){return D.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):Mn(n)&&(n.current=r))},[t])}function Hr(e){return typeof e=="string"||Array.isArray(e)}function Fo(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const Aa=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Da=["initial",...Aa];function jo(e){return Fo(e.animate)||Da.some(t=>Hr(e[t]))}function kp(e){return!!(jo(e)||e.variants)}function vy(e,t){if(jo(e)){const{initial:n,animate:r}=e;return{initial:n===!1||Hr(n)?n:void 0,animate:Hr(r)?r:void 0}}return e.inherit!==!1?t:{}}function xy(e){const{initial:t,animate:n}=vy(e,D.useContext(_o));return D.useMemo(()=>({initial:t,animate:n}),[rc(t),rc(n)])}function rc(e){return Array.isArray(e)?e.join(" "):e}const ic={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Gr={};for(const e in ic)Gr[e]={isEnabled:t=>ic[e].some(n=>!!t[n])};function Sy(e){for(const t in e)Gr[t]={...Gr[t],...e[t]}}const Pp=D.createContext({}),Cp=D.createContext({}),wy=Symbol.for("motionComponentSymbol");function ky({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){e&&Sy(e);function o(l,a){let u;const c={...D.useContext(xp),...l,layoutId:Py(l)},{isStatic:f}=c,d=xy(l),g=r(l,f);if(!f&&No){d.visualElement=gy(i,g,c,t);const y=D.useContext(Cp),v=D.useContext(Sp).strict;d.visualElement&&(u=d.visualElement.loadFeatures(c,v,e,y))}return D.createElement(_o.Provider,{value:d},u&&d.visualElement?D.createElement(u,{visualElement:d.visualElement,...c}):null,n(i,l,yy(g,d.visualElement,a),g,f,d.visualElement))}const s=D.forwardRef(o);return s[wy]=i,s}function Py({layoutId:e}){const t=D.useContext(Pp).id;return t&&e!==void 0?t+"-"+e:e}function Cy(e){function t(r,i={}){return ky(e(r,i))}if(typeof Proxy>"u")return t;const n=new Map;return new Proxy(t,{get:(r,i)=>(n.has(i)||n.set(i,t(i)),n.get(i))})}const Ty=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Ma(e){return typeof e!="string"||e.includes("-")?!1:!!(Ty.indexOf(e)>-1||/[A-Z]/.test(e))}const ao={};function Ey(e){Object.assign(ao,e)}const qr=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],mn=new Set(qr);function Tp(e,{layout:t,layoutId:n}){return mn.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!ao[e]||e==="opacity")}const Ve=e=>!!(e&&e.getVelocity),Vy={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Ly=qr.length;function Ay(e,{enableHardwareAcceleration:t=!0,allowTransformNone:n=!0},r,i){let o="";for(let s=0;s<Ly;s++){const l=qr[s];if(e[l]!==void 0){const a=Vy[l]||l;o+=`${a}(${e[l]}) `}}return t&&!e.z&&(o+="translateZ(0)"),o=o.trim(),i?o=i(e,r?"":o):n&&r&&(o="none"),o}const Ep=e=>t=>typeof t=="string"&&t.startsWith(e),Vp=Ep("--"),vl=Ep("var(--"),Dy=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,My=(e,t)=>t&&typeof e=="number"?t.transform(e):e,Bt=(e,t,n)=>Math.min(Math.max(n,e),t),gn={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},Pr={...gn,transform:e=>Bt(0,1,e)},Si={...gn,default:1},Cr=e=>Math.round(e*1e5)/1e5,Oo=/(-)?([\d]*\.?[\d])+/g,Lp=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,Ry=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function br(e){return typeof e=="string"}const ei=e=>({test:t=>br(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),wt=ei("deg"),it=ei("%"),L=ei("px"),_y=ei("vh"),Ny=ei("vw"),oc={...it,parse:e=>it.parse(e)/100,transform:e=>it.transform(e*100)},sc={...gn,transform:Math.round},Ap={borderWidth:L,borderTopWidth:L,borderRightWidth:L,borderBottomWidth:L,borderLeftWidth:L,borderRadius:L,radius:L,borderTopLeftRadius:L,borderTopRightRadius:L,borderBottomRightRadius:L,borderBottomLeftRadius:L,width:L,maxWidth:L,height:L,maxHeight:L,size:L,top:L,right:L,bottom:L,left:L,padding:L,paddingTop:L,paddingRight:L,paddingBottom:L,paddingLeft:L,margin:L,marginTop:L,marginRight:L,marginBottom:L,marginLeft:L,rotate:wt,rotateX:wt,rotateY:wt,rotateZ:wt,scale:Si,scaleX:Si,scaleY:Si,scaleZ:Si,skew:wt,skewX:wt,skewY:wt,distance:L,translateX:L,translateY:L,translateZ:L,x:L,y:L,z:L,perspective:L,transformPerspective:L,opacity:Pr,originX:oc,originY:oc,originZ:L,zIndex:sc,fillOpacity:Pr,strokeOpacity:Pr,numOctaves:sc};function Ra(e,t,n,r){const{style:i,vars:o,transform:s,transformOrigin:l}=e;let a=!1,u=!1,c=!0;for(const f in t){const d=t[f];if(Vp(f)){o[f]=d;continue}const g=Ap[f],y=My(d,g);if(mn.has(f)){if(a=!0,s[f]=y,!c)continue;d!==(g.default||0)&&(c=!1)}else f.startsWith("origin")?(u=!0,l[f]=y):i[f]=y}if(t.transform||(a||r?i.transform=Ay(e.transform,n,c,r):i.transform&&(i.transform="none")),u){const{originX:f="50%",originY:d="50%",originZ:g=0}=l;i.transformOrigin=`${f} ${d} ${g}`}}const _a=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Dp(e,t,n){for(const r in t)!Ve(t[r])&&!Tp(r,n)&&(e[r]=t[r])}function Fy({transformTemplate:e},t,n){return D.useMemo(()=>{const r=_a();return Ra(r,t,{enableHardwareAcceleration:!n},e),Object.assign({},r.vars,r.style)},[t])}function jy(e,t,n){const r=e.style||{},i={};return Dp(i,r,e),Object.assign(i,Fy(e,t,n)),e.transformValues?e.transformValues(i):i}function Oy(e,t,n){const r={},i=jy(e,t,n);return e.drag&&e.dragListener!==!1&&(r.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=i,r}const zy=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function uo(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||zy.has(e)}let Mp=e=>!uo(e);function Iy(e){e&&(Mp=t=>t.startsWith("on")?!uo(t):e(t))}try{Iy(require("@emotion/is-prop-valid").default)}catch{}function By(e,t,n){const r={};for(const i in e)i==="values"&&typeof e.values=="object"||(Mp(i)||n===!0&&uo(i)||!t&&!uo(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}function lc(e,t,n){return typeof e=="string"?e:L.transform(t+n*e)}function Uy(e,t,n){const r=lc(t,e.x,e.width),i=lc(n,e.y,e.height);return`${r} ${i}`}const $y={offset:"stroke-dashoffset",array:"stroke-dasharray"},Wy={offset:"strokeDashoffset",array:"strokeDasharray"};function Hy(e,t,n=1,r=0,i=!0){e.pathLength=1;const o=i?$y:Wy;e[o.offset]=L.transform(-r);const s=L.transform(t),l=L.transform(n);e[o.array]=`${s} ${l}`}function Na(e,{attrX:t,attrY:n,attrScale:r,originX:i,originY:o,pathLength:s,pathSpacing:l=1,pathOffset:a=0,...u},c,f,d){if(Ra(e,u,c,d),f){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:g,style:y,dimensions:v}=e;g.transform&&(v&&(y.transform=g.transform),delete g.transform),v&&(i!==void 0||o!==void 0||y.transform)&&(y.transformOrigin=Uy(v,i!==void 0?i:.5,o!==void 0?o:.5)),t!==void 0&&(g.x=t),n!==void 0&&(g.y=n),r!==void 0&&(g.scale=r),s!==void 0&&Hy(g,s,l,a,!1)}const Rp=()=>({..._a(),attrs:{}}),Fa=e=>typeof e=="string"&&e.toLowerCase()==="svg";function Gy(e,t,n,r){const i=D.useMemo(()=>{const o=Rp();return Na(o,t,{enableHardwareAcceleration:!1},Fa(r),e.transformTemplate),{...o.attrs,style:{...o.style}}},[t]);if(e.style){const o={};Dp(o,e.style,e),i.style={...o,...i.style}}return i}function Ky(e=!1){return(n,r,i,{latestValues:o},s)=>{const a=(Ma(n)?Gy:Oy)(r,o,s,n),c={...By(r,typeof n=="string",e),...a,ref:i},{children:f}=r,d=D.useMemo(()=>Ve(f)?f.get():f,[f]);return D.createElement(n,{...c,children:d})}}function _p(e,{style:t,vars:n},r,i){Object.assign(e.style,t,i&&i.getProjectionStyles(r));for(const o in n)e.style.setProperty(o,n[o])}const Np=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Fp(e,t,n,r){_p(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(Np.has(i)?i:La(i),t.attrs[i])}function ja(e,t){const{style:n}=e,r={};for(const i in n)(Ve(n[i])||t.style&&Ve(t.style[i])||Tp(i,e))&&(r[i]=n[i]);return r}function jp(e,t){const n=ja(e,t);for(const r in e)if(Ve(e[r])||Ve(t[r])){const i=qr.indexOf(r)!==-1?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r;n[i]=e[r]}return n}function Oa(e,t,n,r={},i={}){return typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),t}function Qy(e){const t=D.useRef(null);return t.current===null&&(t.current=e()),t.current}const co=e=>Array.isArray(e),Xy=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),Yy=e=>co(e)?e[e.length-1]||0:e;function ji(e){const t=Ve(e)?e.get():e;return Xy(t)?t.toValue():t}function Zy({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,i,o){const s={latestValues:Jy(r,i,o,e),renderState:t()};return n&&(s.mount=l=>n(r,l,s)),s}const Op=e=>(t,n)=>{const r=D.useContext(_o),i=D.useContext(Va),o=()=>Zy(e,t,r,i);return n?o():Qy(o)};function Jy(e,t,n,r){const i={},o=r(e,{});for(const d in o)i[d]=ji(o[d]);let{initial:s,animate:l}=e;const a=jo(e),u=kp(e);t&&u&&!a&&e.inherit!==!1&&(s===void 0&&(s=t.initial),l===void 0&&(l=t.animate));let c=n?n.initial===!1:!1;c=c||s===!1;const f=c?l:s;return f&&typeof f!="boolean"&&!Fo(f)&&(Array.isArray(f)?f:[f]).forEach(g=>{const y=Oa(e,g);if(!y)return;const{transitionEnd:v,transition:k,...m}=y;for(const p in m){let h=m[p];if(Array.isArray(h)){const x=c?h.length-1:0;h=h[x]}h!==null&&(i[p]=h)}for(const p in v)i[p]=v[p]}),i}const q=e=>e;class ac{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){const n=this.order.indexOf(t);n!==-1&&(this.order.splice(n,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}function qy(e){let t=new ac,n=new ac,r=0,i=!1,o=!1;const s=new WeakSet,l={schedule:(a,u=!1,c=!1)=>{const f=c&&i,d=f?t:n;return u&&s.add(a),d.add(a)&&f&&i&&(r=t.order.length),a},cancel:a=>{n.remove(a),s.delete(a)},process:a=>{if(i){o=!0;return}if(i=!0,[t,n]=[n,t],n.clear(),r=t.order.length,r)for(let u=0;u<r;u++){const c=t.order[u];c(a),s.has(c)&&(l.schedule(c),e())}i=!1,o&&(o=!1,l.process(a))}};return l}const wi=["prepare","read","update","preRender","render","postRender"],by=40;function ev(e,t){let n=!1,r=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=wi.reduce((f,d)=>(f[d]=qy(()=>n=!0),f),{}),s=f=>o[f].process(i),l=()=>{const f=performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(f-i.timestamp,by),1),i.timestamp=f,i.isProcessing=!0,wi.forEach(s),i.isProcessing=!1,n&&t&&(r=!1,e(l))},a=()=>{n=!0,r=!0,i.isProcessing||e(l)};return{schedule:wi.reduce((f,d)=>{const g=o[d];return f[d]=(y,v=!1,k=!1)=>(n||a(),g.schedule(y,v,k)),f},{}),cancel:f=>wi.forEach(d=>o[d].cancel(f)),state:i,steps:o}}const{schedule:$,cancel:yt,state:pe,steps:ps}=ev(typeof requestAnimationFrame<"u"?requestAnimationFrame:q,!0),tv={useVisualState:Op({scrapeMotionValuesFromProps:jp,createRenderState:Rp,onMount:(e,t,{renderState:n,latestValues:r})=>{$.read(()=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),$.render(()=>{Na(n,r,{enableHardwareAcceleration:!1},Fa(t.tagName),e.transformTemplate),Fp(t,n)})}})},nv={useVisualState:Op({scrapeMotionValuesFromProps:ja,createRenderState:_a})};function rv(e,{forwardMotionProps:t=!1},n,r){return{...Ma(e)?tv:nv,preloadedFeatures:n,useRender:Ky(t),createVisualElement:r,Component:e}}function ut(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const zp=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function zo(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}const iv=e=>t=>zp(t)&&e(t,zo(t));function ft(e,t,n,r){return ut(e,t,iv(n),r)}const ov=(e,t)=>n=>t(e(n)),Ot=(...e)=>e.reduce(ov);function Ip(e){let t=null;return()=>{const n=()=>{t=null};return t===null?(t=e,n):!1}}const uc=Ip("dragHorizontal"),cc=Ip("dragVertical");function Bp(e){let t=!1;if(e==="y")t=cc();else if(e==="x")t=uc();else{const n=uc(),r=cc();n&&r?t=()=>{n(),r()}:(n&&n(),r&&r())}return t}function Up(){const e=Bp(!0);return e?(e(),!1):!0}class Gt{constructor(t){this.isMounted=!1,this.node=t}update(){}}function fc(e,t){const n="pointer"+(t?"enter":"leave"),r="onHover"+(t?"Start":"End"),i=(o,s)=>{if(o.pointerType==="touch"||Up())return;const l=e.getProps();e.animationState&&l.whileHover&&e.animationState.setActive("whileHover",t),l[r]&&$.update(()=>l[r](o,s))};return ft(e.current,n,i,{passive:!e.getProps()[r]})}class sv extends Gt{mount(){this.unmount=Ot(fc(this.node,!0),fc(this.node,!1))}unmount(){}}class lv extends Gt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Ot(ut(this.node.current,"focus",()=>this.onFocus()),ut(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const $p=(e,t)=>t?e===t?!0:$p(e,t.parentElement):!1;function hs(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,zo(n))}class av extends Gt{constructor(){super(...arguments),this.removeStartListeners=q,this.removeEndListeners=q,this.removeAccessibleListeners=q,this.startPointerPress=(t,n)=>{if(this.isPressing)return;this.removeEndListeners();const r=this.node.getProps(),o=ft(window,"pointerup",(l,a)=>{if(!this.checkPressEnd())return;const{onTap:u,onTapCancel:c,globalTapTarget:f}=this.node.getProps();$.update(()=>{!f&&!$p(this.node.current,l.target)?c&&c(l,a):u&&u(l,a)})},{passive:!(r.onTap||r.onPointerUp)}),s=ft(window,"pointercancel",(l,a)=>this.cancelPress(l,a),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=Ot(o,s),this.startPress(t,n)},this.startAccessiblePress=()=>{const t=o=>{if(o.key!=="Enter"||this.isPressing)return;const s=l=>{l.key!=="Enter"||!this.checkPressEnd()||hs("up",(a,u)=>{const{onTap:c}=this.node.getProps();c&&$.update(()=>c(a,u))})};this.removeEndListeners(),this.removeEndListeners=ut(this.node.current,"keyup",s),hs("down",(l,a)=>{this.startPress(l,a)})},n=ut(this.node.current,"keydown",t),r=()=>{this.isPressing&&hs("cancel",(o,s)=>this.cancelPress(o,s))},i=ut(this.node.current,"blur",r);this.removeAccessibleListeners=Ot(n,i)}}startPress(t,n){this.isPressing=!0;const{onTapStart:r,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&$.update(()=>r(t,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!Up()}cancelPress(t,n){if(!this.checkPressEnd())return;const{onTapCancel:r}=this.node.getProps();r&&$.update(()=>r(t,n))}mount(){const t=this.node.getProps(),n=ft(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=ut(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=Ot(n,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const xl=new WeakMap,ms=new WeakMap,uv=e=>{const t=xl.get(e.target);t&&t(e)},cv=e=>{e.forEach(uv)};function fv({root:e,...t}){const n=e||document;ms.has(n)||ms.set(n,{});const r=ms.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(cv,{root:e,...t})),r[i]}function dv(e,t,n){const r=fv(t);return xl.set(e,n),r.observe(e),()=>{xl.delete(e),r.unobserve(e)}}const pv={some:0,all:1};class hv extends Gt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:i="some",once:o}=t,s={root:n?n.current:void 0,rootMargin:r,threshold:typeof i=="number"?i:pv[i]},l=a=>{const{isIntersecting:u}=a;if(this.isInView===u||(this.isInView=u,o&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:f}=this.node.getProps(),d=u?c:f;d&&d(a)};return dv(this.node.current,s,l)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(mv(t,n))&&this.startObserver()}unmount(){}}function mv({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const gv={inView:{Feature:hv},tap:{Feature:av},focus:{Feature:lv},hover:{Feature:sv}};function Wp(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function yv(e){const t={};return e.values.forEach((n,r)=>t[r]=n.get()),t}function vv(e){const t={};return e.values.forEach((n,r)=>t[r]=n.getVelocity()),t}function Io(e,t,n){const r=e.getProps();return Oa(r,t,n!==void 0?n:r.custom,yv(e),vv(e))}let za=q;const ln=e=>e*1e3,dt=e=>e/1e3,xv={current:!1},Hp=e=>Array.isArray(e)&&typeof e[0]=="number";function Gp(e){return!!(!e||typeof e=="string"&&Kp[e]||Hp(e)||Array.isArray(e)&&e.every(Gp))}const pr=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Kp={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:pr([0,.65,.55,1]),circOut:pr([.55,0,1,.45]),backIn:pr([.31,.01,.66,-.59]),backOut:pr([.33,1.53,.69,.99])};function Qp(e){if(e)return Hp(e)?pr(e):Array.isArray(e)?e.map(Qp):Kp[e]}function Sv(e,t,n,{delay:r=0,duration:i,repeat:o=0,repeatType:s="loop",ease:l,times:a}={}){const u={[t]:n};a&&(u.offset=a);const c=Qp(l);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:s==="reverse"?"alternate":"normal"})}function wv(e,{repeat:t,repeatType:n="loop"}){const r=t&&n!=="loop"&&t%2===1?0:e.length-1;return e[r]}const Xp=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,kv=1e-7,Pv=12;function Cv(e,t,n,r,i){let o,s,l=0;do s=t+(n-t)/2,o=Xp(s,r,i)-e,o>0?n=s:t=s;while(Math.abs(o)>kv&&++l<Pv);return s}function ti(e,t,n,r){if(e===t&&n===r)return q;const i=o=>Cv(o,0,1,e,n);return o=>o===0||o===1?o:Xp(i(o),t,r)}const Tv=ti(.42,0,1,1),Ev=ti(0,0,.58,1),Yp=ti(.42,0,.58,1),Vv=e=>Array.isArray(e)&&typeof e[0]!="number",Zp=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Jp=e=>t=>1-e(1-t),Ia=e=>1-Math.sin(Math.acos(e)),qp=Jp(Ia),Lv=Zp(Ia),bp=ti(.33,1.53,.69,.99),Ba=Jp(bp),Av=Zp(Ba),Dv=e=>(e*=2)<1?.5*Ba(e):.5*(2-Math.pow(2,-10*(e-1))),Mv={linear:q,easeIn:Tv,easeInOut:Yp,easeOut:Ev,circIn:Ia,circInOut:Lv,circOut:qp,backIn:Ba,backInOut:Av,backOut:bp,anticipate:Dv},dc=e=>{if(Array.isArray(e)){za(e.length===4);const[t,n,r,i]=e;return ti(t,n,r,i)}else if(typeof e=="string")return Mv[e];return e},Ua=(e,t)=>n=>!!(br(n)&&Ry.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),eh=(e,t,n)=>r=>{if(!br(r))return r;const[i,o,s,l]=r.match(Oo);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(s),alpha:l!==void 0?parseFloat(l):1}},Rv=e=>Bt(0,255,e),gs={...gn,transform:e=>Math.round(Rv(e))},rn={test:Ua("rgb","red"),parse:eh("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+gs.transform(e)+", "+gs.transform(t)+", "+gs.transform(n)+", "+Cr(Pr.transform(r))+")"};function _v(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const Sl={test:Ua("#"),parse:_v,transform:rn.transform},Rn={test:Ua("hsl","hue"),parse:eh("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+it.transform(Cr(t))+", "+it.transform(Cr(n))+", "+Cr(Pr.transform(r))+")"},ye={test:e=>rn.test(e)||Sl.test(e)||Rn.test(e),parse:e=>rn.test(e)?rn.parse(e):Rn.test(e)?Rn.parse(e):Sl.parse(e),transform:e=>br(e)?e:e.hasOwnProperty("red")?rn.transform(e):Rn.transform(e)},K=(e,t,n)=>-n*e+n*t+e;function ys(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function Nv({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let i=0,o=0,s=0;if(!t)i=o=s=n;else{const l=n<.5?n*(1+t):n+t-n*t,a=2*n-l;i=ys(a,l,e+1/3),o=ys(a,l,e),s=ys(a,l,e-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(s*255),alpha:r}}const vs=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},Fv=[Sl,rn,Rn],jv=e=>Fv.find(t=>t.test(e));function pc(e){const t=jv(e);let n=t.parse(e);return t===Rn&&(n=Nv(n)),n}const th=(e,t)=>{const n=pc(e),r=pc(t),i={...n};return o=>(i.red=vs(n.red,r.red,o),i.green=vs(n.green,r.green,o),i.blue=vs(n.blue,r.blue,o),i.alpha=K(n.alpha,r.alpha,o),rn.transform(i))};function Ov(e){var t,n;return isNaN(e)&&br(e)&&(((t=e.match(Oo))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(Lp))===null||n===void 0?void 0:n.length)||0)>0}const nh={regex:Dy,countKey:"Vars",token:"${v}",parse:q},rh={regex:Lp,countKey:"Colors",token:"${c}",parse:ye.parse},ih={regex:Oo,countKey:"Numbers",token:"${n}",parse:gn.parse};function xs(e,{regex:t,countKey:n,token:r,parse:i}){const o=e.tokenised.match(t);o&&(e["num"+n]=o.length,e.tokenised=e.tokenised.replace(t,r),e.values.push(...o.map(i)))}function fo(e){const t=e.toString(),n={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&xs(n,nh),xs(n,rh),xs(n,ih),n}function oh(e){return fo(e).values}function sh(e){const{values:t,numColors:n,numVars:r,tokenised:i}=fo(e),o=t.length;return s=>{let l=i;for(let a=0;a<o;a++)a<r?l=l.replace(nh.token,s[a]):a<r+n?l=l.replace(rh.token,ye.transform(s[a])):l=l.replace(ih.token,Cr(s[a]));return l}}const zv=e=>typeof e=="number"?0:e;function Iv(e){const t=oh(e);return sh(e)(t.map(zv))}const Ut={test:Ov,parse:oh,createTransformer:sh,getAnimatableNone:Iv},lh=(e,t)=>n=>`${n>0?t:e}`;function ah(e,t){return typeof e=="number"?n=>K(e,t,n):ye.test(e)?th(e,t):e.startsWith("var(")?lh(e,t):ch(e,t)}const uh=(e,t)=>{const n=[...e],r=n.length,i=e.map((o,s)=>ah(o,t[s]));return o=>{for(let s=0;s<r;s++)n[s]=i[s](o);return n}},Bv=(e,t)=>{const n={...e,...t},r={};for(const i in n)e[i]!==void 0&&t[i]!==void 0&&(r[i]=ah(e[i],t[i]));return i=>{for(const o in r)n[o]=r[o](i);return n}},ch=(e,t)=>{const n=Ut.createTransformer(t),r=fo(e),i=fo(t);return r.numVars===i.numVars&&r.numColors===i.numColors&&r.numNumbers>=i.numNumbers?Ot(uh(r.values,i.values),n):lh(e,t)},Kr=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},hc=(e,t)=>n=>K(e,t,n);function Uv(e){return typeof e=="number"?hc:typeof e=="string"?ye.test(e)?th:ch:Array.isArray(e)?uh:typeof e=="object"?Bv:hc}function $v(e,t,n){const r=[],i=n||Uv(e[0]),o=e.length-1;for(let s=0;s<o;s++){let l=i(e[s],e[s+1]);if(t){const a=Array.isArray(t)?t[s]||q:t;l=Ot(a,l)}r.push(l)}return r}function fh(e,t,{clamp:n=!0,ease:r,mixer:i}={}){const o=e.length;if(za(o===t.length),o===1)return()=>t[0];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());const s=$v(t,r,i),l=s.length,a=u=>{let c=0;if(l>1)for(;c<e.length-2&&!(u<e[c+1]);c++);const f=Kr(e[c],e[c+1],u);return s[c](f)};return n?u=>a(Bt(e[0],e[o-1],u)):a}function Wv(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=Kr(0,t,r);e.push(K(n,1,i))}}function Hv(e){const t=[0];return Wv(t,e.length-1),t}function Gv(e,t){return e.map(n=>n*t)}function Kv(e,t){return e.map(()=>t||Yp).splice(0,e.length-1)}function po({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const i=Vv(r)?r.map(dc):dc(r),o={done:!1,value:t[0]},s=Gv(n&&n.length===t.length?n:Hv(t),e),l=fh(s,t,{ease:Array.isArray(i)?i:Kv(t,i)});return{calculatedDuration:e,next:a=>(o.value=l(a),o.done=a>=e,o)}}function dh(e,t){return t?e*(1e3/t):0}const Qv=5;function ph(e,t,n){const r=Math.max(t-Qv,0);return dh(n-e(r),t-r)}const Ss=.001,Xv=.01,Yv=10,Zv=.05,Jv=1;function qv({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let i,o,s=1-t;s=Bt(Zv,Jv,s),e=Bt(Xv,Yv,dt(e)),s<1?(i=u=>{const c=u*s,f=c*e,d=c-n,g=wl(u,s),y=Math.exp(-f);return Ss-d/g*y},o=u=>{const f=u*s*e,d=f*n+n,g=Math.pow(s,2)*Math.pow(u,2)*e,y=Math.exp(-f),v=wl(Math.pow(u,2),s);return(-i(u)+Ss>0?-1:1)*((d-g)*y)/v}):(i=u=>{const c=Math.exp(-u*e),f=(u-n)*e+1;return-Ss+c*f},o=u=>{const c=Math.exp(-u*e),f=(n-u)*(e*e);return c*f});const l=5/e,a=e0(i,o,l);if(e=ln(e),isNaN(a))return{stiffness:100,damping:10,duration:e};{const u=Math.pow(a,2)*r;return{stiffness:u,damping:s*2*Math.sqrt(r*u),duration:e}}}const bv=12;function e0(e,t,n){let r=n;for(let i=1;i<bv;i++)r=r-e(r)/t(r);return r}function wl(e,t){return e*Math.sqrt(1-t*t)}const t0=["duration","bounce"],n0=["stiffness","damping","mass"];function mc(e,t){return t.some(n=>e[n]!==void 0)}function r0(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!mc(e,n0)&&mc(e,t0)){const n=qv(e);t={...t,...n,mass:1},t.isResolvedFromDuration=!0}return t}function hh({keyframes:e,restDelta:t,restSpeed:n,...r}){const i=e[0],o=e[e.length-1],s={done:!1,value:i},{stiffness:l,damping:a,mass:u,duration:c,velocity:f,isResolvedFromDuration:d}=r0({...r,velocity:-dt(r.velocity||0)}),g=f||0,y=a/(2*Math.sqrt(l*u)),v=o-i,k=dt(Math.sqrt(l/u)),m=Math.abs(v)<5;n||(n=m?.01:2),t||(t=m?.005:.5);let p;if(y<1){const h=wl(k,y);p=x=>{const S=Math.exp(-y*k*x);return o-S*((g+y*k*v)/h*Math.sin(h*x)+v*Math.cos(h*x))}}else if(y===1)p=h=>o-Math.exp(-k*h)*(v+(g+k*v)*h);else{const h=k*Math.sqrt(y*y-1);p=x=>{const S=Math.exp(-y*k*x),T=Math.min(h*x,300);return o-S*((g+y*k*v)*Math.sinh(T)+h*v*Math.cosh(T))/h}}return{calculatedDuration:d&&c||null,next:h=>{const x=p(h);if(d)s.done=h>=c;else{let S=g;h!==0&&(y<1?S=ph(p,h,x):S=0);const T=Math.abs(S)<=n,C=Math.abs(o-x)<=t;s.done=T&&C}return s.value=s.done?o:x,s}}}function gc({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:l,max:a,restDelta:u=.5,restSpeed:c}){const f=e[0],d={done:!1,value:f},g=P=>l!==void 0&&P<l||a!==void 0&&P>a,y=P=>l===void 0?a:a===void 0||Math.abs(l-P)<Math.abs(a-P)?l:a;let v=n*t;const k=f+v,m=s===void 0?k:s(k);m!==k&&(v=m-f);const p=P=>-v*Math.exp(-P/r),h=P=>m+p(P),x=P=>{const _=p(P),M=h(P);d.done=Math.abs(_)<=u,d.value=d.done?m:M};let S,T;const C=P=>{g(d.value)&&(S=P,T=hh({keyframes:[d.value,y(d.value)],velocity:ph(h,P,d.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return C(0),{calculatedDuration:null,next:P=>{let _=!1;return!T&&S===void 0&&(_=!0,x(P),C(P)),S!==void 0&&P>S?T.next(P-S):(!_&&x(P),d)}}}const i0=e=>{const t=({timestamp:n})=>e(n);return{start:()=>$.update(t,!0),stop:()=>yt(t),now:()=>pe.isProcessing?pe.timestamp:performance.now()}},yc=2e4;function vc(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<yc;)t+=n,r=e.next(t);return t>=yc?1/0:t}const o0={decay:gc,inertia:gc,tween:po,keyframes:po,spring:hh};function ho({autoplay:e=!0,delay:t=0,driver:n=i0,keyframes:r,type:i="keyframes",repeat:o=0,repeatDelay:s=0,repeatType:l="loop",onPlay:a,onStop:u,onComplete:c,onUpdate:f,...d}){let g=1,y=!1,v,k;const m=()=>{k=new Promise(R=>{v=R})};m();let p;const h=o0[i]||po;let x;h!==po&&typeof r[0]!="number"&&(x=fh([0,100],r,{clamp:!1}),r=[0,100]);const S=h({...d,keyframes:r});let T;l==="mirror"&&(T=h({...d,keyframes:[...r].reverse(),velocity:-(d.velocity||0)}));let C="idle",P=null,_=null,M=null;S.calculatedDuration===null&&o&&(S.calculatedDuration=vc(S));const{calculatedDuration:re}=S;let le=1/0,ge=1/0;re!==null&&(le=re+s,ge=le*(o+1)-s);let ie=0;const xt=R=>{if(_===null)return;g>0&&(_=Math.min(_,R)),g<0&&(_=Math.min(R-ge/g,_)),P!==null?ie=P:ie=Math.round(R-_)*g;const W=ie-t*(g>=0?1:-1),Kt=g>=0?W<0:W>ge;ie=Math.max(W,0),C==="finished"&&P===null&&(ie=ge);let qe=ie,yn=S;if(o){const Bo=Math.min(ie,ge)/le;let ni=Math.floor(Bo),Xt=Bo%1;!Xt&&Bo>=1&&(Xt=1),Xt===1&&ni--,ni=Math.min(ni,o+1),!!(ni%2)&&(l==="reverse"?(Xt=1-Xt,s&&(Xt-=s/le)):l==="mirror"&&(yn=T)),qe=Bt(0,1,Xt)*le}const Le=Kt?{done:!1,value:r[0]}:yn.next(qe);x&&(Le.value=x(Le.value));let{done:Qt}=Le;!Kt&&re!==null&&(Qt=g>=0?ie>=ge:ie<=0);const Bh=P===null&&(C==="finished"||C==="running"&&Qt);return f&&f(Le.value),Bh&&E(),Le},Z=()=>{p&&p.stop(),p=void 0},je=()=>{C="idle",Z(),v(),m(),_=M=null},E=()=>{C="finished",c&&c(),Z(),v()},A=()=>{if(y)return;p||(p=n(xt));const R=p.now();a&&a(),P!==null?_=R-P:(!_||C==="finished")&&(_=R),C==="finished"&&m(),M=_,P=null,C="running",p.start()};e&&A();const N={then(R,W){return k.then(R,W)},get time(){return dt(ie)},set time(R){R=ln(R),ie=R,P!==null||!p||g===0?P=R:_=p.now()-R/g},get duration(){const R=S.calculatedDuration===null?vc(S):S.calculatedDuration;return dt(R)},get speed(){return g},set speed(R){R===g||!p||(g=R,N.time=dt(ie))},get state(){return C},play:A,pause:()=>{C="paused",P=ie},stop:()=>{y=!0,C!=="idle"&&(C="idle",u&&u(),je())},cancel:()=>{M!==null&&xt(M),je()},complete:()=>{C="finished"},sample:R=>(_=0,xt(R))};return N}function s0(e){let t;return()=>(t===void 0&&(t=e()),t)}const l0=s0(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),a0=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),ki=10,u0=2e4,c0=(e,t)=>t.type==="spring"||e==="backgroundColor"||!Gp(t.ease);function f0(e,t,{onUpdate:n,onComplete:r,...i}){if(!(l0()&&a0.has(t)&&!i.repeatDelay&&i.repeatType!=="mirror"&&i.damping!==0&&i.type!=="inertia"))return!1;let s=!1,l,a,u=!1;const c=()=>{a=new Promise(h=>{l=h})};c();let{keyframes:f,duration:d=300,ease:g,times:y}=i;if(c0(t,i)){const h=ho({...i,repeat:0,delay:0});let x={done:!1,value:f[0]};const S=[];let T=0;for(;!x.done&&T<u0;)x=h.sample(T),S.push(x.value),T+=ki;y=void 0,f=S,d=T-ki,g="linear"}const v=Sv(e.owner.current,t,f,{...i,duration:d,ease:g,times:y}),k=()=>{u=!1,v.cancel()},m=()=>{u=!0,$.update(k),l(),c()};return v.onfinish=()=>{u||(e.set(wv(f,i)),r&&r(),m())},{then(h,x){return a.then(h,x)},attachTimeline(h){return v.timeline=h,v.onfinish=null,q},get time(){return dt(v.currentTime||0)},set time(h){v.currentTime=ln(h)},get speed(){return v.playbackRate},set speed(h){v.playbackRate=h},get duration(){return dt(d)},play:()=>{s||(v.play(),yt(k))},pause:()=>v.pause(),stop:()=>{if(s=!0,v.playState==="idle")return;const{currentTime:h}=v;if(h){const x=ho({...i,autoplay:!1});e.setWithVelocity(x.sample(h-ki).value,x.sample(h).value,ki)}m()},complete:()=>{u||v.finish()},cancel:m}}function d0({keyframes:e,delay:t,onUpdate:n,onComplete:r}){const i=()=>(n&&n(e[e.length-1]),r&&r(),{time:0,speed:1,duration:0,play:q,pause:q,stop:q,then:o=>(o(),Promise.resolve()),cancel:q,complete:q});return t?ho({keyframes:[0,1],duration:0,delay:t,onComplete:i}):i()}const p0={type:"spring",stiffness:500,damping:25,restSpeed:10},h0=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),m0={type:"keyframes",duration:.8},g0={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},y0=(e,{keyframes:t})=>t.length>2?m0:mn.has(e)?e.startsWith("scale")?h0(t[1]):p0:g0,kl=(e,t)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(Ut.test(t)||t==="0")&&!t.startsWith("url(")),v0=new Set(["brightness","contrast","saturate","opacity"]);function x0(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(Oo)||[];if(!r)return e;const i=n.replace(r,"");let o=v0.has(t)?1:0;return r!==n&&(o*=100),t+"("+o+i+")"}const S0=/([a-z-]*)\(.*?\)/g,Pl={...Ut,getAnimatableNone:e=>{const t=e.match(S0);return t?t.map(x0).join(" "):e}},w0={...Ap,color:ye,backgroundColor:ye,outlineColor:ye,fill:ye,stroke:ye,borderColor:ye,borderTopColor:ye,borderRightColor:ye,borderBottomColor:ye,borderLeftColor:ye,filter:Pl,WebkitFilter:Pl},$a=e=>w0[e];function mh(e,t){let n=$a(e);return n!==Pl&&(n=Ut),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const gh=e=>/^0[^.\s]+$/.test(e);function k0(e){if(typeof e=="number")return e===0;if(e!==null)return e==="none"||e==="0"||gh(e)}function P0(e,t,n,r){const i=kl(t,n);let o;Array.isArray(n)?o=[...n]:o=[null,n];const s=r.from!==void 0?r.from:e.get();let l;const a=[];for(let u=0;u<o.length;u++)o[u]===null&&(o[u]=u===0?s:o[u-1]),k0(o[u])&&a.push(u),typeof o[u]=="string"&&o[u]!=="none"&&o[u]!=="0"&&(l=o[u]);if(i&&a.length&&l)for(let u=0;u<a.length;u++){const c=a[u];o[c]=mh(t,l)}return o}function C0({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:l,from:a,elapsed:u,...c}){return!!Object.keys(c).length}function Wa(e,t){return e[t]||e.default||e}const T0={skipAnimations:!1},Ha=(e,t,n,r={})=>i=>{const o=Wa(r,e)||{},s=o.delay||r.delay||0;let{elapsed:l=0}=r;l=l-ln(s);const a=P0(t,e,n,o),u=a[0],c=a[a.length-1],f=kl(e,u),d=kl(e,c);let g={keyframes:a,velocity:t.getVelocity(),ease:"easeOut",...o,delay:-l,onUpdate:y=>{t.set(y),o.onUpdate&&o.onUpdate(y)},onComplete:()=>{i(),o.onComplete&&o.onComplete()}};if(C0(o)||(g={...g,...y0(e,g)}),g.duration&&(g.duration=ln(g.duration)),g.repeatDelay&&(g.repeatDelay=ln(g.repeatDelay)),!f||!d||xv.current||o.type===!1||T0.skipAnimations)return d0(g);if(!r.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){const y=f0(t,e,g);if(y)return y}return ho(g)};function mo(e){return!!(Ve(e)&&e.add)}const yh=e=>/^\-?\d*\.?\d+$/.test(e);function Ga(e,t){e.indexOf(t)===-1&&e.push(t)}function Ka(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class Qa{constructor(){this.subscriptions=[]}add(t){return Ga(this.subscriptions,t),()=>Ka(this.subscriptions,t)}notify(t,n,r){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](t,n,r);else for(let o=0;o<i;o++){const s=this.subscriptions[o];s&&s(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const E0=e=>!isNaN(parseFloat(e));class V0{constructor(t,n={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,i=!0)=>{this.prev=this.current,this.current=r;const{delta:o,timestamp:s}=pe;this.lastUpdated!==s&&(this.timeDelta=o,this.lastUpdated=s,$.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>$.postRender(this.velocityCheck),this.velocityCheck=({timestamp:r})=>{r!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=E0(this.current),this.owner=n.owner}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new Qa);const r=this.events[t].add(n);return t==="change"?()=>{r(),$.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=t,this.timeDelta=r}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?dh(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Xn(e,t){return new V0(e,t)}const vh=e=>t=>t.test(e),L0={test:e=>e==="auto",parse:e=>e},xh=[gn,L,it,wt,Ny,_y,L0],lr=e=>xh.find(vh(e)),A0=[...xh,ye,Ut],D0=e=>A0.find(vh(e));function M0(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Xn(n))}function R0(e,t){const n=Io(e,t);let{transitionEnd:r={},transition:i={},...o}=n?e.makeTargetAnimatable(n,!1):{};o={...o,...r};for(const s in o){const l=Yy(o[s]);M0(e,s,l)}}function _0(e,t,n){var r,i;const o=Object.keys(t).filter(l=>!e.hasValue(l)),s=o.length;if(s)for(let l=0;l<s;l++){const a=o[l],u=t[a];let c=null;Array.isArray(u)&&(c=u[0]),c===null&&(c=(i=(r=n[a])!==null&&r!==void 0?r:e.readValue(a))!==null&&i!==void 0?i:t[a]),c!=null&&(typeof c=="string"&&(yh(c)||gh(c))?c=parseFloat(c):!D0(c)&&Ut.test(u)&&(c=mh(a,u)),e.addValue(a,Xn(c,{owner:e})),n[a]===void 0&&(n[a]=c),c!==null&&e.setBaseTarget(a,c))}}function N0(e,t){return t?(t[e]||t.default||t).from:void 0}function F0(e,t,n){const r={};for(const i in e){const o=N0(i,t);if(o!==void 0)r[i]=o;else{const s=n.getValue(i);s&&(r[i]=s.get())}}return r}function j0({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function O0(e,t){const n=e.get();if(Array.isArray(t)){for(let r=0;r<t.length;r++)if(t[r]!==n)return!0}else return n!==t}function Sh(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:s,...l}=e.makeTargetAnimatable(t);const a=e.getValue("willChange");r&&(o=r);const u=[],c=i&&e.animationState&&e.animationState.getState()[i];for(const f in l){const d=e.getValue(f),g=l[f];if(!d||g===void 0||c&&j0(c,f))continue;const y={delay:n,elapsed:0,...Wa(o||{},f)};if(window.HandoffAppearAnimations){const m=e.getProps()[wp];if(m){const p=window.HandoffAppearAnimations(m,f,d,$);p!==null&&(y.elapsed=p,y.isHandoff=!0)}}let v=!y.isHandoff&&!O0(d,g);if(y.type==="spring"&&(d.getVelocity()||y.velocity)&&(v=!1),d.animation&&(v=!1),v)continue;d.start(Ha(f,d,g,e.shouldReduceMotion&&mn.has(f)?{type:!1}:y));const k=d.animation;mo(a)&&(a.add(f),k.then(()=>a.remove(f))),u.push(k)}return s&&Promise.all(u).then(()=>{s&&R0(e,s)}),u}function Cl(e,t,n={}){const r=Io(e,t,n.custom);let{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);const o=r?()=>Promise.all(Sh(e,r,n)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(a=0)=>{const{delayChildren:u=0,staggerChildren:c,staggerDirection:f}=i;return z0(e,t,u+a,c,f,n)}:()=>Promise.resolve(),{when:l}=i;if(l){const[a,u]=l==="beforeChildren"?[o,s]:[s,o];return a().then(()=>u())}else return Promise.all([o(),s(n.delay)])}function z0(e,t,n=0,r=0,i=1,o){const s=[],l=(e.variantChildren.size-1)*r,a=i===1?(u=0)=>u*r:(u=0)=>l-u*r;return Array.from(e.variantChildren).sort(I0).forEach((u,c)=>{u.notify("AnimationStart",t),s.push(Cl(u,t,{...o,delay:n+a(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(s)}function I0(e,t){return e.sortNodePosition(t)}function B0(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const i=t.map(o=>Cl(e,o,n));r=Promise.all(i)}else if(typeof t=="string")r=Cl(e,t,n);else{const i=typeof t=="function"?Io(e,t,n.custom):t;r=Promise.all(Sh(e,i,n))}return r.then(()=>e.notify("AnimationComplete",t))}const U0=[...Aa].reverse(),$0=Aa.length;function W0(e){return t=>Promise.all(t.map(({animation:n,options:r})=>B0(e,n,r)))}function H0(e){let t=W0(e);const n=K0();let r=!0;const i=(a,u)=>{const c=Io(e,u);if(c){const{transition:f,transitionEnd:d,...g}=c;a={...a,...g,...d}}return a};function o(a){t=a(e)}function s(a,u){const c=e.getProps(),f=e.getVariantContext(!0)||{},d=[],g=new Set;let y={},v=1/0;for(let m=0;m<$0;m++){const p=U0[m],h=n[p],x=c[p]!==void 0?c[p]:f[p],S=Hr(x),T=p===u?h.isActive:null;T===!1&&(v=m);let C=x===f[p]&&x!==c[p]&&S;if(C&&r&&e.manuallyAnimateOnMount&&(C=!1),h.protectedKeys={...y},!h.isActive&&T===null||!x&&!h.prevProp||Fo(x)||typeof x=="boolean")continue;let _=G0(h.prevProp,x)||p===u&&h.isActive&&!C&&S||m>v&&S,M=!1;const re=Array.isArray(x)?x:[x];let le=re.reduce(i,{});T===!1&&(le={});const{prevResolvedValues:ge={}}=h,ie={...ge,...le},xt=Z=>{_=!0,g.has(Z)&&(M=!0,g.delete(Z)),h.needsAnimating[Z]=!0};for(const Z in ie){const je=le[Z],E=ge[Z];if(y.hasOwnProperty(Z))continue;let A=!1;co(je)&&co(E)?A=!Wp(je,E):A=je!==E,A?je!==void 0?xt(Z):g.add(Z):je!==void 0&&g.has(Z)?xt(Z):h.protectedKeys[Z]=!0}h.prevProp=x,h.prevResolvedValues=le,h.isActive&&(y={...y,...le}),r&&e.blockInitialAnimation&&(_=!1),_&&(!C||M)&&d.push(...re.map(Z=>({animation:Z,options:{type:p,...a}})))}if(g.size){const m={};g.forEach(p=>{const h=e.getBaseTarget(p);h!==void 0&&(m[p]=h)}),d.push({animation:m})}let k=!!d.length;return r&&(c.initial===!1||c.initial===c.animate)&&!e.manuallyAnimateOnMount&&(k=!1),r=!1,k?t(d):Promise.resolve()}function l(a,u,c){var f;if(n[a].isActive===u)return Promise.resolve();(f=e.variantChildren)===null||f===void 0||f.forEach(g=>{var y;return(y=g.animationState)===null||y===void 0?void 0:y.setActive(a,u)}),n[a].isActive=u;const d=s(c,a);for(const g in n)n[g].protectedKeys={};return d}return{animateChanges:s,setActive:l,setAnimateFunction:o,getState:()=>n}}function G0(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Wp(t,e):!1}function Yt(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function K0(){return{animate:Yt(!0),whileInView:Yt(),whileHover:Yt(),whileTap:Yt(),whileDrag:Yt(),whileFocus:Yt(),exit:Yt()}}class Q0 extends Gt{constructor(t){super(t),t.animationState||(t.animationState=H0(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();this.unmount(),Fo(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){}}let X0=0;class Y0 extends Gt{constructor(){super(...arguments),this.id=X0++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n,custom:r}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;const o=this.node.animationState.setActive("exit",!t,{custom:r??this.node.getProps().custom});n&&!t&&o.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const Z0={animation:{Feature:Q0},exit:{Feature:Y0}},xc=(e,t)=>Math.abs(e-t);function J0(e,t){const n=xc(e.x,t.x),r=xc(e.y,t.y);return Math.sqrt(n**2+r**2)}class wh{constructor(t,n,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const f=ks(this.lastMoveEventInfo,this.history),d=this.startEvent!==null,g=J0(f.offset,{x:0,y:0})>=3;if(!d&&!g)return;const{point:y}=f,{timestamp:v}=pe;this.history.push({...y,timestamp:v});const{onStart:k,onMove:m}=this.handlers;d||(k&&k(this.lastMoveEvent,f),this.startEvent=this.lastMoveEvent),m&&m(this.lastMoveEvent,f)},this.handlePointerMove=(f,d)=>{this.lastMoveEvent=f,this.lastMoveEventInfo=ws(d,this.transformPagePoint),$.update(this.updatePoint,!0)},this.handlePointerUp=(f,d)=>{this.end();const{onEnd:g,onSessionEnd:y,resumeAnimation:v}=this.handlers;if(this.dragSnapToOrigin&&v&&v(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const k=ks(f.type==="pointercancel"?this.lastMoveEventInfo:ws(d,this.transformPagePoint),this.history);this.startEvent&&g&&g(f,k),y&&y(f,k)},!zp(t))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=r,this.contextWindow=i||window;const s=zo(t),l=ws(s,this.transformPagePoint),{point:a}=l,{timestamp:u}=pe;this.history=[{...a,timestamp:u}];const{onSessionStart:c}=n;c&&c(t,ks(l,this.history)),this.removeListeners=Ot(ft(this.contextWindow,"pointermove",this.handlePointerMove),ft(this.contextWindow,"pointerup",this.handlePointerUp),ft(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),yt(this.updatePoint)}}function ws(e,t){return t?{point:t(e.point)}:e}function Sc(e,t){return{x:e.x-t.x,y:e.y-t.y}}function ks({point:e},t){return{point:e,delta:Sc(e,kh(t)),offset:Sc(e,q0(t)),velocity:b0(t,.1)}}function q0(e){return e[0]}function kh(e){return e[e.length-1]}function b0(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=kh(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>ln(t)));)n--;if(!r)return{x:0,y:0};const o=dt(i.timestamp-r.timestamp);if(o===0)return{x:0,y:0};const s={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}function _e(e){return e.max-e.min}function Tl(e,t=0,n=.01){return Math.abs(e-t)<=n}function wc(e,t,n,r=.5){e.origin=r,e.originPoint=K(t.min,t.max,e.origin),e.scale=_e(n)/_e(t),(Tl(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=K(n.min,n.max,e.origin)-e.originPoint,(Tl(e.translate)||isNaN(e.translate))&&(e.translate=0)}function Tr(e,t,n,r){wc(e.x,t.x,n.x,r?r.originX:void 0),wc(e.y,t.y,n.y,r?r.originY:void 0)}function kc(e,t,n){e.min=n.min+t.min,e.max=e.min+_e(t)}function e1(e,t,n){kc(e.x,t.x,n.x),kc(e.y,t.y,n.y)}function Pc(e,t,n){e.min=t.min-n.min,e.max=e.min+_e(t)}function Er(e,t,n){Pc(e.x,t.x,n.x),Pc(e.y,t.y,n.y)}function t1(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?K(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?K(n,e,r.max):Math.min(e,n)),e}function Cc(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function n1(e,{top:t,left:n,bottom:r,right:i}){return{x:Cc(e.x,n,i),y:Cc(e.y,t,r)}}function Tc(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function r1(e,t){return{x:Tc(e.x,t.x),y:Tc(e.y,t.y)}}function i1(e,t){let n=.5;const r=_e(e),i=_e(t);return i>r?n=Kr(t.min,t.max-r,e.min):r>i&&(n=Kr(e.min,e.max-i,t.min)),Bt(0,1,n)}function o1(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const El=.35;function s1(e=El){return e===!1?e=0:e===!0&&(e=El),{x:Ec(e,"left","right"),y:Ec(e,"top","bottom")}}function Ec(e,t,n){return{min:Vc(e,t),max:Vc(e,n)}}function Vc(e,t){return typeof e=="number"?e:e[t]||0}const Lc=()=>({translate:0,scale:1,origin:0,originPoint:0}),_n=()=>({x:Lc(),y:Lc()}),Ac=()=>({min:0,max:0}),b=()=>({x:Ac(),y:Ac()});function ze(e){return[e("x"),e("y")]}function Ph({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function l1({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function a1(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function Ps(e){return e===void 0||e===1}function Vl({scale:e,scaleX:t,scaleY:n}){return!Ps(e)||!Ps(t)||!Ps(n)}function qt(e){return Vl(e)||Ch(e)||e.z||e.rotate||e.rotateX||e.rotateY}function Ch(e){return Dc(e.x)||Dc(e.y)}function Dc(e){return e&&e!=="0%"}function go(e,t,n){const r=e-n,i=t*r;return n+i}function Mc(e,t,n,r,i){return i!==void 0&&(e=go(e,i,r)),go(e,n,r)+t}function Ll(e,t=0,n=1,r,i){e.min=Mc(e.min,t,n,r,i),e.max=Mc(e.max,t,n,r,i)}function Th(e,{x:t,y:n}){Ll(e.x,t.translate,t.scale,t.originPoint),Ll(e.y,n.translate,n.scale,n.originPoint)}function u1(e,t,n,r=!1){const i=n.length;if(!i)return;t.x=t.y=1;let o,s;for(let l=0;l<i;l++){o=n[l],s=o.projectionDelta;const a=o.instance;a&&a.style&&a.style.display==="contents"||(r&&o.options.layoutScroll&&o.scroll&&o!==o.root&&Nn(e,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,Th(e,s)),r&&qt(o.latestValues)&&Nn(e,o.latestValues))}t.x=Rc(t.x),t.y=Rc(t.y)}function Rc(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function Ct(e,t){e.min=e.min+t,e.max=e.max+t}function _c(e,t,[n,r,i]){const o=t[i]!==void 0?t[i]:.5,s=K(e.min,e.max,o);Ll(e,t[n],t[r],s,t.scale)}const c1=["x","scaleX","originX"],f1=["y","scaleY","originY"];function Nn(e,t){_c(e.x,t,c1),_c(e.y,t,f1)}function Eh(e,t){return Ph(a1(e.getBoundingClientRect(),t))}function d1(e,t,n){const r=Eh(e,n),{scroll:i}=t;return i&&(Ct(r.x,i.offset.x),Ct(r.y,i.offset.y)),r}const Vh=({current:e})=>e?e.ownerDocument.defaultView:null,p1=new WeakMap;class h1{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=b(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const i=c=>{const{dragSnapToOrigin:f}=this.getProps();f?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(zo(c,"page").point)},o=(c,f)=>{const{drag:d,dragPropagation:g,onDragStart:y}=this.getProps();if(d&&!g&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=Bp(d),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ze(k=>{let m=this.getAxisMotionValue(k).get()||0;if(it.test(m)){const{projection:p}=this.visualElement;if(p&&p.layout){const h=p.layout.layoutBox[k];h&&(m=_e(h)*(parseFloat(m)/100))}}this.originPoint[k]=m}),y&&$.update(()=>y(c,f),!1,!0);const{animationState:v}=this.visualElement;v&&v.setActive("whileDrag",!0)},s=(c,f)=>{const{dragPropagation:d,dragDirectionLock:g,onDirectionLock:y,onDrag:v}=this.getProps();if(!d&&!this.openGlobalLock)return;const{offset:k}=f;if(g&&this.currentDirection===null){this.currentDirection=m1(k),this.currentDirection!==null&&y&&y(this.currentDirection);return}this.updateAxis("x",f.point,k),this.updateAxis("y",f.point,k),this.visualElement.render(),v&&v(c,f)},l=(c,f)=>this.stop(c,f),a=()=>ze(c=>{var f;return this.getAnimationState(c)==="paused"&&((f=this.getAxisMotionValue(c).animation)===null||f===void 0?void 0:f.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new wh(t,{onSessionStart:i,onStart:o,onMove:s,onSessionEnd:l,resumeAnimation:a},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:Vh(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:o}=this.getProps();o&&$.update(()=>o(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:i}=this.getProps();if(!r||!Pi(t,i,this.currentDirection))return;const o=this.getAxisMotionValue(t);let s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=t1(s,this.constraints[t],this.elastic[t])),o.set(s)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,o=this.constraints;n&&Mn(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=n1(i.layoutBox,n):this.constraints=!1,this.elastic=s1(r),o!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&ze(s=>{this.getAxisMotionValue(s)&&(this.constraints[s]=o1(i.layoutBox[s],this.constraints[s]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!Mn(t))return!1;const r=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=d1(r,i.root,this.visualElement.getTransformPagePoint());let s=r1(i.layout.layoutBox,o);if(n){const l=n(l1(s));this.hasMutatedConstraints=!!l,l&&(s=Ph(l))}return s}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:i,dragTransition:o,dragSnapToOrigin:s,onDragTransitionEnd:l}=this.getProps(),a=this.constraints||{},u=ze(c=>{if(!Pi(c,n,this.currentDirection))return;let f=a&&a[c]||{};s&&(f={min:0,max:0});const d=i?200:1e6,g=i?40:1e7,y={type:"inertia",velocity:r?t[c]:0,bounceStiffness:d,bounceDamping:g,timeConstant:750,restDelta:1,restSpeed:10,...o,...f};return this.startAxisValueAnimation(c,y)});return Promise.all(u).then(l)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return r.start(Ha(t,r,0,n))}stopAnimation(){ze(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){ze(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n="_drag"+t.toUpperCase(),r=this.visualElement.getProps(),i=r[n];return i||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){ze(n=>{const{drag:r}=this.getProps();if(!Pi(n,r,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:s,max:l}=i.layout.layoutBox[n];o.set(t[n]-K(s,l,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!Mn(n)||!r||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};ze(s=>{const l=this.getAxisMotionValue(s);if(l){const a=l.get();i[s]=i1({min:a,max:a},this.constraints[s])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),ze(s=>{if(!Pi(s,t,null))return;const l=this.getAxisMotionValue(s),{min:a,max:u}=this.constraints[s];l.set(K(a,u,i[s]))})}addListeners(){if(!this.visualElement.current)return;p1.set(this.visualElement,this);const t=this.visualElement.current,n=ft(t,"pointerdown",a=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(a)}),r=()=>{const{dragConstraints:a}=this.getProps();Mn(a)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,o=i.addEventListener("measure",r);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),r();const s=ut(window,"resize",()=>this.scalePositionWithinConstraints()),l=i.addEventListener("didUpdate",({delta:a,hasLayoutChanged:u})=>{this.isDragging&&u&&(ze(c=>{const f=this.getAxisMotionValue(c);f&&(this.originPoint[c]+=a[c].translate,f.set(f.get()+a[c].translate))}),this.visualElement.render())});return()=>{s(),n(),o(),l&&l()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:s=El,dragMomentum:l=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:i,dragConstraints:o,dragElastic:s,dragMomentum:l}}}function Pi(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function m1(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class g1 extends Gt{constructor(t){super(t),this.removeGroupControls=q,this.removeListeners=q,this.controls=new h1(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||q}unmount(){this.removeGroupControls(),this.removeListeners()}}const Nc=e=>(t,n)=>{e&&$.update(()=>e(t,n))};class y1 extends Gt{constructor(){super(...arguments),this.removePointerDownListener=q}onPointerDown(t){this.session=new wh(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Vh(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:Nc(t),onStart:Nc(n),onMove:r,onEnd:(o,s)=>{delete this.session,i&&$.update(()=>i(o,s))}}}mount(){this.removePointerDownListener=ft(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}function v1(){const e=D.useContext(Va);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,i=D.useId();return D.useEffect(()=>r(i),[]),!t&&n?[!1,()=>n&&n(i)]:[!0]}const Oi={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Fc(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const ar={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(L.test(e))e=parseFloat(e);else return e;const n=Fc(e,t.target.x),r=Fc(e,t.target.y);return`${n}% ${r}%`}},x1={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,i=Ut.parse(e);if(i.length>5)return r;const o=Ut.createTransformer(e),s=typeof i[0]!="number"?1:0,l=n.x.scale*t.x,a=n.y.scale*t.y;i[0+s]/=l,i[1+s]/=a;const u=K(l,a,.5);return typeof i[2+s]=="number"&&(i[2+s]/=u),typeof i[3+s]=="number"&&(i[3+s]/=u),o(i)}};class S1 extends jl.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:i}=this.props,{projection:o}=t;Ey(w1),o&&(n.group&&n.group.add(o),r&&r.register&&i&&r.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),Oi.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:i,isPresent:o}=this.props,s=r.projection;return s&&(s.isPresent=o,i||t.layoutDependency!==n||n===void 0?s.willUpdate():this.safeToRemove(),t.isPresent!==o&&(o?s.promote():s.relegate()||$.postRender(()=>{const l=s.getStack();(!l||!l.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Lh(e){const[t,n]=v1(),r=D.useContext(Pp);return jl.createElement(S1,{...e,layoutGroup:r,switchLayoutGroup:D.useContext(Cp),isPresent:t,safeToRemove:n})}const w1={borderRadius:{...ar,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ar,borderTopRightRadius:ar,borderBottomLeftRadius:ar,borderBottomRightRadius:ar,boxShadow:x1},Ah=["TopLeft","TopRight","BottomLeft","BottomRight"],k1=Ah.length,jc=e=>typeof e=="string"?parseFloat(e):e,Oc=e=>typeof e=="number"||L.test(e);function P1(e,t,n,r,i,o){i?(e.opacity=K(0,n.opacity!==void 0?n.opacity:1,C1(r)),e.opacityExit=K(t.opacity!==void 0?t.opacity:1,0,T1(r))):o&&(e.opacity=K(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let s=0;s<k1;s++){const l=`border${Ah[s]}Radius`;let a=zc(t,l),u=zc(n,l);if(a===void 0&&u===void 0)continue;a||(a=0),u||(u=0),a===0||u===0||Oc(a)===Oc(u)?(e[l]=Math.max(K(jc(a),jc(u),r),0),(it.test(u)||it.test(a))&&(e[l]+="%")):e[l]=u}(t.rotate||n.rotate)&&(e.rotate=K(t.rotate||0,n.rotate||0,r))}function zc(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const C1=Dh(0,.5,qp),T1=Dh(.5,.95,q);function Dh(e,t,n){return r=>r<e?0:r>t?1:n(Kr(e,t,r))}function Ic(e,t){e.min=t.min,e.max=t.max}function Oe(e,t){Ic(e.x,t.x),Ic(e.y,t.y)}function Bc(e,t,n,r,i){return e-=t,e=go(e,1/n,r),i!==void 0&&(e=go(e,1/i,r)),e}function E1(e,t=0,n=1,r=.5,i,o=e,s=e){if(it.test(t)&&(t=parseFloat(t),t=K(s.min,s.max,t/100)-s.min),typeof t!="number")return;let l=K(o.min,o.max,r);e===o&&(l-=t),e.min=Bc(e.min,t,n,l,i),e.max=Bc(e.max,t,n,l,i)}function Uc(e,t,[n,r,i],o,s){E1(e,t[n],t[r],t[i],t.scale,o,s)}const V1=["x","scaleX","originX"],L1=["y","scaleY","originY"];function $c(e,t,n,r){Uc(e.x,t,V1,n?n.x:void 0,r?r.x:void 0),Uc(e.y,t,L1,n?n.y:void 0,r?r.y:void 0)}function Wc(e){return e.translate===0&&e.scale===1}function Mh(e){return Wc(e.x)&&Wc(e.y)}function A1(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}function Rh(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function Hc(e){return _e(e.x)/_e(e.y)}class D1{constructor(){this.members=[]}add(t){Ga(this.members,t),t.scheduleRender()}remove(t){if(Ka(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(i=>t===i);if(n===0)return!1;let r;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){r=o;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;i===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Gc(e,t,n){let r="";const i=e.x.translate/t.x,o=e.y.translate/t.y;if((i||o)&&(r=`translate3d(${i}px, ${o}px, 0) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{rotate:a,rotateX:u,rotateY:c}=n;a&&(r+=`rotate(${a}deg) `),u&&(r+=`rotateX(${u}deg) `),c&&(r+=`rotateY(${c}deg) `)}const s=e.x.scale*t.x,l=e.y.scale*t.y;return(s!==1||l!==1)&&(r+=`scale(${s}, ${l})`),r||"none"}const M1=(e,t)=>e.depth-t.depth;class R1{constructor(){this.children=[],this.isDirty=!1}add(t){Ga(this.children,t),this.isDirty=!0}remove(t){Ka(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(M1),this.isDirty=!1,this.children.forEach(t)}}function _1(e,t){const n=performance.now(),r=({timestamp:i})=>{const o=i-n;o>=t&&(yt(r),e(o-t))};return $.read(r,!0),()=>yt(r)}function N1(e){window.MotionDebug&&window.MotionDebug.record(e)}function F1(e){return e instanceof SVGElement&&e.tagName!=="svg"}function j1(e,t,n){const r=Ve(e)?e:Xn(e);return r.start(Ha("",r,t,n)),r.animation}const Kc=["","X","Y","Z"],O1={visibility:"hidden"},Qc=1e3;let z1=0;const bt={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function _h({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(s={},l=t==null?void 0:t()){this.id=z1++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,bt.totalNodes=bt.resolvedTargetDeltas=bt.recalculatedProjection=0,this.nodes.forEach(U1),this.nodes.forEach(K1),this.nodes.forEach(Q1),this.nodes.forEach($1),N1(bt)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=s,this.root=l?l.root||l:this,this.path=l?[...l.path,l]:[],this.parent=l,this.depth=l?l.depth+1:0;for(let a=0;a<this.path.length;a++)this.path[a].shouldResetTransform=!0;this.root===this&&(this.nodes=new R1)}addEventListener(s,l){return this.eventHandlers.has(s)||this.eventHandlers.set(s,new Qa),this.eventHandlers.get(s).add(l)}notifyListeners(s,...l){const a=this.eventHandlers.get(s);a&&a.notify(...l)}hasListeners(s){return this.eventHandlers.has(s)}mount(s,l=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=F1(s),this.instance=s;const{layoutId:a,layout:u,visualElement:c}=this.options;if(c&&!c.current&&c.mount(s),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),l&&(u||a)&&(this.isLayoutDirty=!0),e){let f;const d=()=>this.root.updateBlockedByResize=!1;e(s,()=>{this.root.updateBlockedByResize=!0,f&&f(),f=_1(d,250),Oi.hasAnimatedSinceResize&&(Oi.hasAnimatedSinceResize=!1,this.nodes.forEach(Yc))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&c&&(a||u)&&this.addEventListener("didUpdate",({delta:f,hasLayoutChanged:d,hasRelativeTargetChanged:g,layout:y})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const v=this.options.transition||c.getDefaultTransition()||q1,{onLayoutAnimationStart:k,onLayoutAnimationComplete:m}=c.getProps(),p=!this.targetLayout||!Rh(this.targetLayout,y)||g,h=!d&&g;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||h||d&&(p||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(f,h);const x={...Wa(v,"layout"),onPlay:k,onComplete:m};(c.shouldReduceMotion||this.options.layoutRoot)&&(x.delay=0,x.type=!1),this.startAnimation(x)}else d||Yc(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=y})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const s=this.getStack();s&&s.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,yt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(X1),this.animationId++)}getTransformTemplate(){const{visualElement:s}=this.options;return s&&s.getProps().transformTemplate}willUpdate(s=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const f=this.path[c];f.shouldResetTransform=!0,f.updateScroll("snapshot"),f.options.layoutRoot&&f.willUpdate(!1)}const{layoutId:l,layout:a}=this.options;if(l===void 0&&!a)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),s&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Xc);return}this.isUpdating||this.nodes.forEach(H1),this.isUpdating=!1,this.nodes.forEach(G1),this.nodes.forEach(I1),this.nodes.forEach(B1),this.clearAllSnapshots();const l=performance.now();pe.delta=Bt(0,1e3/60,l-pe.timestamp),pe.timestamp=l,pe.isProcessing=!0,ps.update.process(pe),ps.preRender.process(pe),ps.render.process(pe),pe.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(W1),this.sharedNodes.forEach(Y1)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,$.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){$.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();const s=this.layout;this.layout=this.measure(!1),this.layoutCorrected=b(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:l}=this.options;l&&l.notify("LayoutMeasure",this.layout.layoutBox,s?s.layoutBox:void 0)}updateScroll(s="measure"){let l=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===s&&(l=!1),l&&(this.scroll={animationId:this.root.animationId,phase:s,isRoot:r(this.instance),offset:n(this.instance)})}resetTransform(){if(!i)return;const s=this.isLayoutDirty||this.shouldResetTransform,l=this.projectionDelta&&!Mh(this.projectionDelta),a=this.getTransformTemplate(),u=a?a(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;s&&(l||qt(this.latestValues)||c)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(s=!0){const l=this.measurePageBox();let a=this.removeElementScroll(l);return s&&(a=this.removeTransform(a)),b1(a),{animationId:this.root.animationId,measuredBox:l,layoutBox:a,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:s}=this.options;if(!s)return b();const l=s.measureViewportBox(),{scroll:a}=this.root;return a&&(Ct(l.x,a.offset.x),Ct(l.y,a.offset.y)),l}removeElementScroll(s){const l=b();Oe(l,s);for(let a=0;a<this.path.length;a++){const u=this.path[a],{scroll:c,options:f}=u;if(u!==this.root&&c&&f.layoutScroll){if(c.isRoot){Oe(l,s);const{scroll:d}=this.root;d&&(Ct(l.x,-d.offset.x),Ct(l.y,-d.offset.y))}Ct(l.x,c.offset.x),Ct(l.y,c.offset.y)}}return l}applyTransform(s,l=!1){const a=b();Oe(a,s);for(let u=0;u<this.path.length;u++){const c=this.path[u];!l&&c.options.layoutScroll&&c.scroll&&c!==c.root&&Nn(a,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),qt(c.latestValues)&&Nn(a,c.latestValues)}return qt(this.latestValues)&&Nn(a,this.latestValues),a}removeTransform(s){const l=b();Oe(l,s);for(let a=0;a<this.path.length;a++){const u=this.path[a];if(!u.instance||!qt(u.latestValues))continue;Vl(u.latestValues)&&u.updateSnapshot();const c=b(),f=u.measurePageBox();Oe(c,f),$c(l,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return qt(this.latestValues)&&$c(l,this.latestValues),l}setTargetDelta(s){this.targetDelta=s,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(s){this.options={...this.options,...s,crossfade:s.crossfade!==void 0?s.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==pe.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(s=!1){var l;const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==a;if(!(s||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((l=this.parent)===null||l===void 0)&&l.isProjectionDirty||this.attemptToResolveRelativeTarget))return;const{layout:f,layoutId:d}=this.options;if(!(!this.layout||!(f||d))){if(this.resolvedRelativeTargetAt=pe.timestamp,!this.targetDelta&&!this.relativeTarget){const g=this.getClosestProjectingParent();g&&g.layout&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=b(),this.relativeTargetOrigin=b(),Er(this.relativeTargetOrigin,this.layout.layoutBox,g.layout.layoutBox),Oe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=b(),this.targetWithTransforms=b()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),e1(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Oe(this.target,this.layout.layoutBox),Th(this.target,this.targetDelta)):Oe(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const g=this.getClosestProjectingParent();g&&!!g.resumingFrom==!!this.resumingFrom&&!g.options.layoutScroll&&g.target&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=b(),this.relativeTargetOrigin=b(),Er(this.relativeTargetOrigin,this.target,g.target),Oe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}bt.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Vl(this.parent.latestValues)||Ch(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var s;const l=this.getLead(),a=!!this.resumingFrom||this!==l;let u=!0;if((this.isProjectionDirty||!((s=this.parent)===null||s===void 0)&&s.isProjectionDirty)&&(u=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===pe.timestamp&&(u=!1),u)return;const{layout:c,layoutId:f}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||f))return;Oe(this.layoutCorrected,this.layout.layoutBox);const d=this.treeScale.x,g=this.treeScale.y;u1(this.layoutCorrected,this.treeScale,this.path,a),l.layout&&!l.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(l.target=l.layout.layoutBox);const{target:y}=l;if(!y){this.projectionTransform&&(this.projectionDelta=_n(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=_n(),this.projectionDeltaWithTransform=_n());const v=this.projectionTransform;Tr(this.projectionDelta,this.layoutCorrected,y,this.latestValues),this.projectionTransform=Gc(this.projectionDelta,this.treeScale),(this.projectionTransform!==v||this.treeScale.x!==d||this.treeScale.y!==g)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",y)),bt.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(s=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),s){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(s,l=!1){const a=this.snapshot,u=a?a.latestValues:{},c={...this.latestValues},f=_n();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!l;const d=b(),g=a?a.source:void 0,y=this.layout?this.layout.source:void 0,v=g!==y,k=this.getStack(),m=!k||k.members.length<=1,p=!!(v&&!m&&this.options.crossfade===!0&&!this.path.some(J1));this.animationProgress=0;let h;this.mixTargetDelta=x=>{const S=x/1e3;Zc(f.x,s.x,S),Zc(f.y,s.y,S),this.setTargetDelta(f),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Er(d,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Z1(this.relativeTarget,this.relativeTargetOrigin,d,S),h&&A1(this.relativeTarget,h)&&(this.isProjectionDirty=!1),h||(h=b()),Oe(h,this.relativeTarget)),v&&(this.animationValues=c,P1(c,u,this.latestValues,S,p,m)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=S},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(s){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(yt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=$.update(()=>{Oi.hasAnimatedSinceResize=!0,this.currentAnimation=j1(0,Qc,{...s,onUpdate:l=>{this.mixTargetDelta(l),s.onUpdate&&s.onUpdate(l)},onComplete:()=>{s.onComplete&&s.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const s=this.getStack();s&&s.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Qc),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const s=this.getLead();let{targetWithTransforms:l,target:a,layout:u,latestValues:c}=s;if(!(!l||!a||!u)){if(this!==s&&this.layout&&u&&Nh(this.options.animationType,this.layout.layoutBox,u.layoutBox)){a=this.target||b();const f=_e(this.layout.layoutBox.x);a.x.min=s.target.x.min,a.x.max=a.x.min+f;const d=_e(this.layout.layoutBox.y);a.y.min=s.target.y.min,a.y.max=a.y.min+d}Oe(l,a),Nn(l,c),Tr(this.projectionDeltaWithTransform,this.layoutCorrected,l,c)}}registerSharedNode(s,l){this.sharedNodes.has(s)||this.sharedNodes.set(s,new D1),this.sharedNodes.get(s).add(l);const u=l.options.initialPromotionConfig;l.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(l):void 0})}isLead(){const s=this.getStack();return s?s.lead===this:!0}getLead(){var s;const{layoutId:l}=this.options;return l?((s=this.getStack())===null||s===void 0?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:l}=this.options;return l?(s=this.getStack())===null||s===void 0?void 0:s.prevLead:void 0}getStack(){const{layoutId:s}=this.options;if(s)return this.root.sharedNodes.get(s)}promote({needsReset:s,transition:l,preserveFollowOpacity:a}={}){const u=this.getStack();u&&u.promote(this,a),s&&(this.projectionDelta=void 0,this.needsReset=!0),l&&this.setOptions({transition:l})}relegate(){const s=this.getStack();return s?s.relegate(this):!1}resetRotation(){const{visualElement:s}=this.options;if(!s)return;let l=!1;const{latestValues:a}=s;if((a.rotate||a.rotateX||a.rotateY||a.rotateZ)&&(l=!0),!l)return;const u={};for(let c=0;c<Kc.length;c++){const f="rotate"+Kc[c];a[f]&&(u[f]=a[f],s.setStaticValue(f,0))}s.render();for(const c in u)s.setStaticValue(c,u[c]);s.scheduleRender()}getProjectionStyles(s){var l,a;if(!this.instance||this.isSVG)return;if(!this.isVisible)return O1;const u={visibility:""},c=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=ji(s==null?void 0:s.pointerEvents)||"",u.transform=c?c(this.latestValues,""):"none",u;const f=this.getLead();if(!this.projectionDelta||!this.layout||!f.target){const v={};return this.options.layoutId&&(v.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,v.pointerEvents=ji(s==null?void 0:s.pointerEvents)||""),this.hasProjected&&!qt(this.latestValues)&&(v.transform=c?c({},""):"none",this.hasProjected=!1),v}const d=f.animationValues||f.latestValues;this.applyTransformsToTarget(),u.transform=Gc(this.projectionDeltaWithTransform,this.treeScale,d),c&&(u.transform=c(d,u.transform));const{x:g,y}=this.projectionDelta;u.transformOrigin=`${g.origin*100}% ${y.origin*100}% 0`,f.animationValues?u.opacity=f===this?(a=(l=d.opacity)!==null&&l!==void 0?l:this.latestValues.opacity)!==null&&a!==void 0?a:1:this.preserveOpacity?this.latestValues.opacity:d.opacityExit:u.opacity=f===this?d.opacity!==void 0?d.opacity:"":d.opacityExit!==void 0?d.opacityExit:0;for(const v in ao){if(d[v]===void 0)continue;const{correct:k,applyTo:m}=ao[v],p=u.transform==="none"?d[v]:k(d[v],f);if(m){const h=m.length;for(let x=0;x<h;x++)u[m[x]]=p}else u[v]=p}return this.options.layoutId&&(u.pointerEvents=f===this?ji(s==null?void 0:s.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(s=>{var l;return(l=s.currentAnimation)===null||l===void 0?void 0:l.stop()}),this.root.nodes.forEach(Xc),this.root.sharedNodes.clear()}}}function I1(e){e.updateLayout()}function B1(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:i}=e.layout,{animationType:o}=e.options,s=n.source!==e.layout.source;o==="size"?ze(f=>{const d=s?n.measuredBox[f]:n.layoutBox[f],g=_e(d);d.min=r[f].min,d.max=d.min+g}):Nh(o,n.layoutBox,r)&&ze(f=>{const d=s?n.measuredBox[f]:n.layoutBox[f],g=_e(r[f]);d.max=d.min+g,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[f].max=e.relativeTarget[f].min+g)});const l=_n();Tr(l,r,n.layoutBox);const a=_n();s?Tr(a,e.applyTransform(i,!0),n.measuredBox):Tr(a,r,n.layoutBox);const u=!Mh(l);let c=!1;if(!e.resumeFrom){const f=e.getClosestProjectingParent();if(f&&!f.resumeFrom){const{snapshot:d,layout:g}=f;if(d&&g){const y=b();Er(y,n.layoutBox,d.layoutBox);const v=b();Er(v,r,g.layoutBox),Rh(y,v)||(c=!0),f.options.layoutRoot&&(e.relativeTarget=v,e.relativeTargetOrigin=y,e.relativeParent=f)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:a,layoutDelta:l,hasLayoutChanged:u,hasRelativeTargetChanged:c})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function U1(e){bt.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function $1(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function W1(e){e.clearSnapshot()}function Xc(e){e.clearMeasurements()}function H1(e){e.isLayoutDirty=!1}function G1(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Yc(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function K1(e){e.resolveTargetDelta()}function Q1(e){e.calcProjection()}function X1(e){e.resetRotation()}function Y1(e){e.removeLeadSnapshot()}function Zc(e,t,n){e.translate=K(t.translate,0,n),e.scale=K(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function Jc(e,t,n,r){e.min=K(t.min,n.min,r),e.max=K(t.max,n.max,r)}function Z1(e,t,n,r){Jc(e.x,t.x,n.x,r),Jc(e.y,t.y,n.y,r)}function J1(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const q1={duration:.45,ease:[.4,0,.1,1]},qc=e=>typeof navigator<"u"&&navigator.userAgent.toLowerCase().includes(e),bc=qc("applewebkit/")&&!qc("chrome/")?Math.round:q;function ef(e){e.min=bc(e.min),e.max=bc(e.max)}function b1(e){ef(e.x),ef(e.y)}function Nh(e,t,n){return e==="position"||e==="preserve-aspect"&&!Tl(Hc(t),Hc(n),.2)}const ex=_h({attachResizeListener:(e,t)=>ut(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Cs={current:void 0},Fh=_h({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Cs.current){const e=new ex({});e.mount(window),e.setOptions({layoutScroll:!0}),Cs.current=e}return Cs.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),tx={pan:{Feature:y1},drag:{Feature:g1,ProjectionNode:Fh,MeasureLayout:Lh}},nx=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function rx(e){const t=nx.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}function Al(e,t,n=1){const[r,i]=rx(e);if(!r)return;const o=window.getComputedStyle(t).getPropertyValue(r);if(o){const s=o.trim();return yh(s)?parseFloat(s):s}else return vl(i)?Al(i,t,n+1):i}function ix(e,{...t},n){const r=e.current;if(!(r instanceof Element))return{target:t,transitionEnd:n};n&&(n={...n}),e.values.forEach(i=>{const o=i.get();if(!vl(o))return;const s=Al(o,r);s&&i.set(s)});for(const i in t){const o=t[i];if(!vl(o))continue;const s=Al(o,r);s&&(t[i]=s,n||(n={}),n[i]===void 0&&(n[i]=o))}return{target:t,transitionEnd:n}}const ox=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),jh=e=>ox.has(e),sx=e=>Object.keys(e).some(jh),tf=e=>e===gn||e===L,nf=(e,t)=>parseFloat(e.split(", ")[t]),rf=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/);if(i)return nf(i[1],t);{const o=r.match(/^matrix\((.+)\)$/);return o?nf(o[1],e):0}},lx=new Set(["x","y","z"]),ax=qr.filter(e=>!lx.has(e));function ux(e){const t=[];return ax.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t.length&&e.render(),t}const Yn={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:rf(4,13),y:rf(5,14)};Yn.translateX=Yn.x;Yn.translateY=Yn.y;const cx=(e,t,n)=>{const r=t.measureViewportBox(),i=t.current,o=getComputedStyle(i),{display:s}=o,l={};s==="none"&&t.setStaticValue("display",e.display||"block"),n.forEach(u=>{l[u]=Yn[u](r,o)}),t.render();const a=t.measureViewportBox();return n.forEach(u=>{const c=t.getValue(u);c&&c.jump(l[u]),e[u]=Yn[u](a,o)}),e},fx=(e,t,n={},r={})=>{t={...t},r={...r};const i=Object.keys(t).filter(jh);let o=[],s=!1;const l=[];if(i.forEach(a=>{const u=e.getValue(a);if(!e.hasValue(a))return;let c=n[a],f=lr(c);const d=t[a];let g;if(co(d)){const y=d.length,v=d[0]===null?1:0;c=d[v],f=lr(c);for(let k=v;k<y&&d[k]!==null;k++)g?za(lr(d[k])===g):g=lr(d[k])}else g=lr(d);if(f!==g)if(tf(f)&&tf(g)){const y=u.get();typeof y=="string"&&u.set(parseFloat(y)),typeof d=="string"?t[a]=parseFloat(d):Array.isArray(d)&&g===L&&(t[a]=d.map(parseFloat))}else f!=null&&f.transform&&(g!=null&&g.transform)&&(c===0||d===0)?c===0?u.set(g.transform(c)):t[a]=f.transform(d):(s||(o=ux(e),s=!0),l.push(a),r[a]=r[a]!==void 0?r[a]:t[a],u.jump(d))}),l.length){const a=l.indexOf("height")>=0?window.pageYOffset:null,u=cx(t,e,l);return o.length&&o.forEach(([c,f])=>{e.getValue(c).set(f)}),e.render(),No&&a!==null&&window.scrollTo({top:a}),{target:u,transitionEnd:r}}else return{target:t,transitionEnd:r}};function dx(e,t,n,r){return sx(t)?fx(e,t,n,r):{target:t,transitionEnd:r}}const px=(e,t,n,r)=>{const i=ix(e,t,r);return t=i.target,r=i.transitionEnd,dx(e,t,n,r)},Dl={current:null},Oh={current:!1};function hx(){if(Oh.current=!0,!!No)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Dl.current=e.matches;e.addListener(t),t()}else Dl.current=!1}function mx(e,t,n){const{willChange:r}=t;for(const i in t){const o=t[i],s=n[i];if(Ve(o))e.addValue(i,o),mo(r)&&r.add(i);else if(Ve(s))e.addValue(i,Xn(o,{owner:e})),mo(r)&&r.remove(i);else if(s!==o)if(e.hasValue(i)){const l=e.getValue(i);!l.hasAnimated&&l.set(o)}else{const l=e.getStaticValue(i);e.addValue(i,Xn(l!==void 0?l:o,{owner:e}))}}for(const i in n)t[i]===void 0&&e.removeValue(i);return t}const of=new WeakMap,zh=Object.keys(Gr),gx=zh.length,sf=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],yx=Da.length;class vx{constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:i,visualState:o},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>$.render(this.render,!1,!0);const{latestValues:l,renderState:a}=o;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=a,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=s,this.isControllingVariants=jo(n),this.isVariantNode=kp(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:u,...c}=this.scrapeMotionValuesFromProps(n,{});for(const f in c){const d=c[f];l[f]!==void 0&&Ve(d)&&(d.set(l[f],!1),mo(u)&&u.add(f))}}scrapeMotionValuesFromProps(t,n){return{}}mount(t){this.current=t,of.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),Oh.current||hx(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Dl.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){of.delete(this.current),this.projection&&this.projection.unmount(),yt(this.notifyUpdate),yt(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,n){const r=mn.has(t),i=n.on("change",s=>{this.latestValues[t]=s,this.props.onUpdate&&$.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{i(),o()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}loadFeatures({children:t,...n},r,i,o){let s,l;for(let a=0;a<gx;a++){const u=zh[a],{isEnabled:c,Feature:f,ProjectionNode:d,MeasureLayout:g}=Gr[u];d&&(s=d),c(n)&&(!this.features[u]&&f&&(this.features[u]=new f(this)),g&&(l=g))}if((this.type==="html"||this.type==="svg")&&!this.projection&&s){this.projection=new s(this.latestValues,this.parent&&this.parent.projection);const{layoutId:a,layout:u,drag:c,dragConstraints:f,layoutScroll:d,layoutRoot:g}=n;this.projection.setOptions({layoutId:a,layout:u,alwaysMeasureLayout:!!c||f&&Mn(f),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof u=="string"?u:"both",initialPromotionConfig:o,layoutScroll:d,layoutRoot:g})}return l}updateFeatures(){for(const t in this.features){const n=this.features[t];n.isMounted?n.update():(n.mount(),n.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):b()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}makeTargetAnimatable(t,n=!0){return this.makeTargetAnimatableFromInstance(t,this.props,n)}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<sf.length;r++){const i=sf[r];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const o=t["on"+i];o&&(this.propEventSubscriptions[i]=this.on(i,o))}this.prevMotionValues=mx(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const r=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}const n={};for(let r=0;r<yx;r++){const i=Da[r],o=this.props[i];(Hr(o)||o===!1)&&(n[i]=o)}return n}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){n!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,n)),this.values.set(t,n),this.latestValues[t]=n.get()}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=Xn(n,{owner:this}),this.addValue(t,r)),r}readValue(t){var n;return this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(n=this.getBaseTargetFromProps(this.props,t))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,t,this.options)}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props,i=typeof r=="string"||typeof r=="object"?(n=Oa(this.props,r))===null||n===void 0?void 0:n[t]:void 0;if(r&&i!==void 0)return i;const o=this.getBaseTargetFromProps(this.props,t);return o!==void 0&&!Ve(o)?o:this.initialValues[t]!==void 0&&i===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new Qa),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class Ih extends vx{sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:n,...r},{transformValues:i},o){let s=F0(r,t||{},this);if(i&&(n&&(n=i(n)),r&&(r=i(r)),s&&(s=i(s))),o){_0(this,r,s);const l=px(this,r,s,n);n=l.transitionEnd,r=l.target}return{transition:t,transitionEnd:n,...r}}}function xx(e){return window.getComputedStyle(e)}class Sx extends Ih{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,n){if(mn.has(n)){const r=$a(n);return r&&r.default||0}else{const r=xx(t),i=(Vp(n)?r.getPropertyValue(n):r[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:n}){return Eh(t,n)}build(t,n,r,i){Ra(t,n,r,i.transformTemplate)}scrapeMotionValuesFromProps(t,n){return ja(t,n)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Ve(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}renderInstance(t,n,r,i){_p(t,n,r,i)}}class wx extends Ih{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(mn.has(n)){const r=$a(n);return r&&r.default||0}return n=Np.has(n)?n:La(n),t.getAttribute(n)}measureInstanceViewportBox(){return b()}scrapeMotionValuesFromProps(t,n){return jp(t,n)}build(t,n,r,i){Na(t,n,r,this.isSVGTag,i.transformTemplate)}renderInstance(t,n,r,i){Fp(t,n,r,i)}mount(t){this.isSVGTag=Fa(t.tagName),super.mount(t)}}const kx=(e,t)=>Ma(e)?new wx(t,{enableHardwareAcceleration:!1}):new Sx(t,{enableHardwareAcceleration:!0}),Px={layout:{ProjectionNode:Fh,MeasureLayout:Lh}},Cx={...Z0,...gv,...tx,...Px},Ml=Cy((e,t)=>rv(e,t,Cx,kx));function Tx({message:e}){const[n,r]=D.useState(Array(25).fill(""));return D.useEffect(()=>{const i=e.toUpperCase().replace(/[^A-Z]/g,"").padEnd(25,"X").slice(0,25);let o=Array.from({length:5},()=>Array(5).fill("")),s=[0,1,0,-1],l=[1,0,-1,0],a=0,u=0,c=0;for(let f=0;f<25;f++){o[u][c]=i[f];let d=u+s[a],g=c+l[a];(d<0||g<0||d>=5||g>=5||o[d][g]!=="")&&(a=(a+1)%4,d=u+s[a],g=c+l[a]),u=d,c=g}r(o.flat())},[e]),O.jsx("div",{style:{display:"grid",gridTemplateColumns:"repeat(5, 1fr)",gap:"8px",marginTop:"20px",maxWidth:"300px",margin:"20px auto"},children:n.map((i,o)=>O.jsx(Ml.div,{style:{width:"50px",height:"50px",display:"flex",alignItems:"center",justifyContent:"center",border:"2px solid #f59e0b",borderRadius:"8px",backgroundColor:"#fef3c7",color:"#92400e",fontWeight:"bold",fontSize:"18px"},initial:{scale:0,rotate:-180},animate:{scale:1,rotate:0},transition:{delay:o*.1,type:"spring",stiffness:200,damping:10},children:i},o))})}function Ex({message:e}){const[t,n]=D.useState(Array(25).fill("")),[r,i]=D.useState("");return D.useEffect(()=>{const o=e.toUpperCase().replace(/[^A-Z]/g,"").padEnd(25,"X").slice(0,25);let s=Array.from({length:5},()=>Array(5).fill("")),l=0;for(let k=0;k<5;k++)for(let m=0;m<5;m++)s[m][k]=o[l++]||"X";let a=[0,1,0,-1],u=[1,0,-1,0],c=0,f=0,d=0,g="",y=Array.from({length:5},()=>Array(5).fill(!1));for(let k=0;k<25;k++){g+=s[f][d],y[f][d]=!0;let m=f+a[c],p=d+u[c];(m<0||p<0||m>=5||p>=5||y[m][p])&&(c=(c+1)%4,m=f+a[c],p=d+u[c]),f=m,d=p}let v=[];for(let k=0;k<5;k++)for(let m=0;m<5;m++)v.push(s[k][m]);n(v),i(g.replace(/X+$/,""))},[e]),O.jsxs("div",{children:[O.jsx("div",{style:{display:"grid",gridTemplateColumns:"repeat(5, 1fr)",gap:"8px",marginTop:"20px",maxWidth:"300px",margin:"20px auto"},children:t.map((o,s)=>O.jsx(Ml.div,{style:{width:"50px",height:"50px",display:"flex",alignItems:"center",justifyContent:"center",border:"2px solid #3b82f6",borderRadius:"8px",backgroundColor:"#dbeafe",color:"#1e40af",fontWeight:"bold",fontSize:"18px"},initial:{scale:0,rotate:180},animate:{scale:1,rotate:0},transition:{delay:s*.08,type:"spring",stiffness:150,damping:12},children:o},s))}),r&&O.jsx(Ml.div,{style:{marginTop:"20px",padding:"15px",backgroundColor:"#dcfce7",border:"2px solid #16a34a",borderRadius:"8px",textAlign:"center"},initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:2.5},children:O.jsxs("strong",{style:{color:"#15803d"},children:["Decrypted Message: ",r]})})]})}function Vx(){const[e,t]=D.useState("MEETATNOON"),[n,r]=D.useState("encrypt"),i=()=>{if(n!=="encrypt")return"";const o=e.toUpperCase().replace(/[^A-Z]/g,"").padEnd(25,"X").slice(0,25);let s=Array.from({length:5},()=>Array(5).fill("")),l=[0,1,0,-1],a=[1,0,-1,0],u=0,c=0,f=0;for(let g=0;g<25;g++){s[c][f]=o[g];let y=c+l[u],v=f+a[u];(y<0||v<0||y>=5||v>=5||s[y][v]!=="")&&(u=(u+1)%4,y=c+l[u],v=f+a[u]),c=y,f=v}let d="";for(let g=0;g<5;g++)for(let y=0;y<5;y++)d+=s[y][g];return d.replace(/X+$/,"")};return O.jsxs("div",{style:{backgroundColor:"#ffffff",border:"2px solid #e5e7eb",borderRadius:"16px",padding:"30px",boxShadow:"0 10px 25px rgba(0,0,0,0.1)"},children:[O.jsx("h2",{style:{fontSize:"28px",fontWeight:"bold",color:"#374151",marginBottom:"20px",textAlign:"center"},children:"🔄 Spiral Cipher Visualizer"}),O.jsxs("div",{style:{marginBottom:"20px"},children:[O.jsx("label",{style:{display:"block",marginBottom:"8px",fontWeight:"bold",color:"#374151"},children:"Enter your message:"}),O.jsx("input",{type:"text",value:e,onChange:o=>t(o.target.value),placeholder:"Enter your message here...",style:{width:"100%",padding:"12px 16px",border:"2px solid #d1d5db",borderRadius:"8px",fontSize:"16px",outline:"none",transition:"border-color 0.2s"},onFocus:o=>o.target.style.borderColor="#3b82f6",onBlur:o=>o.target.style.borderColor="#d1d5db"})]}),O.jsxs("div",{style:{display:"flex",gap:"12px",marginBottom:"30px",justifyContent:"center"},children:[O.jsx("button",{onClick:()=>r("encrypt"),style:{padding:"12px 24px",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"16px",fontWeight:"bold",transition:"all 0.2s",backgroundColor:n==="encrypt"?"#f59e0b":"#fef3c7",color:n==="encrypt"?"white":"#92400e",transform:n==="encrypt"?"scale(1.05)":"scale(1)"},children:"🕷️ Encrypt"}),O.jsx("button",{onClick:()=>r("decrypt"),style:{padding:"12px 24px",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"16px",fontWeight:"bold",transition:"all 0.2s",backgroundColor:n==="decrypt"?"#3b82f6":"#dbeafe",color:n==="decrypt"?"white":"#1e40af",transform:n==="decrypt"?"scale(1.05)":"scale(1)"},children:"🔓 Decrypt"})]}),n==="encrypt"?O.jsxs("div",{children:[O.jsx("h3",{style:{textAlign:"center",color:"#92400e",marginBottom:"10px"},children:"Encryption: Spiral Pattern"}),O.jsx(Tx,{message:e}),e&&O.jsx("div",{style:{marginTop:"20px",padding:"15px",backgroundColor:"#fef3c7",border:"2px solid #f59e0b",borderRadius:"8px",textAlign:"center"},children:O.jsxs("strong",{style:{color:"#92400e"},children:["Encrypted: ",i()]})})]}):O.jsxs("div",{children:[O.jsx("h3",{style:{textAlign:"center",color:"#1e40af",marginBottom:"10px"},children:"Decryption: Reverse Spiral"}),O.jsx(Ex,{message:e})]})]})}function Lx(){return O.jsx("div",{style:{minHeight:"100vh",backgroundColor:"#fffbee",padding:"20px"},children:O.jsxs("div",{style:{maxWidth:"800px",margin:"0 auto"},children:[O.jsx("h1",{style:{textAlign:"center",color:"#92400e",fontSize:"2.5rem",marginBottom:"2rem"},children:"🕷️ Ant-Bee-Spider Cipher Tool 🐝"}),O.jsx(Vx,{})]})})}Ts.createRoot(document.getElementById("root")).render(O.jsx(jl.StrictMode,{children:O.jsx(Lx,{})}));
