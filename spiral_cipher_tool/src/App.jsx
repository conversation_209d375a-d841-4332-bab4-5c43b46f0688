import SpiralCipherTool from './SpiralCipherTool';
import './index.css';

function App() {
  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#fffbee',
      padding: '20px'
    }}>
      <div style={{
        maxWidth: '800px',
        margin: '0 auto'
      }}>
        <h1 style={{
          textAlign: 'center',
          color: '#92400e',
          fontSize: '2.5rem',
          marginBottom: '1rem'
        }}>
          🕷️🐝🐜 Nature's Cipher Laboratory
        </h1>
        <p style={{
          textAlign: 'center',
          color: '#6b7280',
          fontSize: '1.2rem',
          marginBottom: '2rem',
          fontStyle: 'italic'
        }}>
          Encryption inspired by spider webs, bee dances, and ant trails
        </p>
        <SpiralCipherTool />
      </div>
    </div>
  );
}

export default App;