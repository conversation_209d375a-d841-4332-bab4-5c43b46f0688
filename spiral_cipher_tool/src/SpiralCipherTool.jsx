import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

// Spiral Grid Component for Encryption
function EncryptionGrid({ message }) {
  const gridSize = 5;
  const [grid, setGrid] = useState(Array(25).fill(''));

  useEffect(() => {
    // Clean the message: uppercase, letters only, pad to 25 chars
    const cleaned = message.toUpperCase().replace(/[^A-Z]/g, '').padEnd(25, 'X').slice(0, 25);
    
    // Create 2D grid
    let spiralGrid = Array.from({ length: gridSize }, () => Array(gridSize).fill(''));
    
    // Spiral direction vectors: right, down, left, up
    let dx = [0, 1, 0, -1];
    let dy = [1, 0, -1, 0];
    let dir = 0; // Start going right
    let x = 0, y = 0; // Start at top-left
    
    // Fill the grid in spiral pattern
    for (let i = 0; i < 25; i++) {
      spiralGrid[x][y] = cleaned[i];
      
      // Calculate next position
      let nx = x + dx[dir];
      let ny = y + dy[dir];
      
      // Check if we need to turn (hit boundary or filled cell)
      if (nx < 0 || ny < 0 || nx >= gridSize || ny >= gridSize || spiralGrid[nx][ny] !== '') {
        dir = (dir + 1) % 4; // Turn clockwise
        nx = x + dx[dir];
        ny = y + dy[dir];
      }
      
      x = nx;
      y = ny;
    }
    
    setGrid(spiralGrid.flat());
  }, [message]);

  return (
    <div style={{
      display: 'grid',
      gridTemplateColumns: 'repeat(5, 1fr)',
      gap: '8px',
      marginTop: '20px',
      maxWidth: '300px',
      margin: '20px auto'
    }}>
      {grid.map((letter, idx) => (
        <motion.div
          key={idx}
          style={{
            width: '50px',
            height: '50px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            border: '2px solid #f59e0b',
            borderRadius: '8px',
            backgroundColor: '#fef3c7',
            color: '#92400e',
            fontWeight: 'bold',
            fontSize: '18px'
          }}
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ 
            delay: idx * 0.1,
            type: "spring",
            stiffness: 200,
            damping: 10
          }}
        >
          {letter}
        </motion.div>
      ))}
    </div>
  );
}

// Spiral Grid Component for Decryption
function DecryptionGrid({ message }) {
  const gridSize = 5;
  const [grid, setGrid] = useState(Array(25).fill(''));
  const [decrypted, setDecrypted] = useState('');

  useEffect(() => {
    // Clean the encrypted message
    const cleaned = message.toUpperCase().replace(/[^A-Z]/g, '').padEnd(25, 'X').slice(0, 25);

    // For decryption: Fill grid column by column (reverse of encryption)
    let decryptGrid = Array.from({ length: 5 }, () => Array(5).fill(''));
    let idx = 0;

    // Fill column by column with the encrypted text
    for (let col = 0; col < 5; col++) {
      for (let row = 0; row < 5; row++) {
        decryptGrid[row][col] = cleaned[idx++] || 'X';
      }
    }

    // Now read in spiral pattern to get the original message
    let dx = [0, 1, 0, -1]; // right, down, left, up
    let dy = [1, 0, -1, 0];
    let dir = 0;
    let x = 0, y = 0;
    let result = '';
    let visited = Array.from({ length: 5 }, () => Array(5).fill(false));

    // Read in spiral pattern
    for (let i = 0; i < 25; i++) {
      result += decryptGrid[x][y];
      visited[x][y] = true;

      let nx = x + dx[dir];
      let ny = y + dy[dir];

      if (nx < 0 || ny < 0 || nx >= 5 || ny >= 5 || visited[nx][ny]) {
        dir = (dir + 1) % 4;
        nx = x + dx[dir];
        ny = y + dy[dir];
      }

      x = nx;
      y = ny;
    }

    // Set the grid for display (flatten the 2D array)
    let tempGrid = [];
    for (let row = 0; row < 5; row++) {
      for (let col = 0; col < 5; col++) {
        tempGrid.push(decryptGrid[row][col]);
      }
    }
    setGrid(tempGrid);

    // Set the decrypted result
    setDecrypted(result.replace(/X+$/, '')); // Remove trailing X's
  }, [message]);

  return (
    <div>
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(5, 1fr)',
        gap: '8px',
        marginTop: '20px',
        maxWidth: '300px',
        margin: '20px auto'
      }}>
        {grid.map((letter, idx) => (
          <motion.div
            key={idx}
            style={{
              width: '50px',
              height: '50px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: '2px solid #3b82f6',
              borderRadius: '8px',
              backgroundColor: '#dbeafe',
              color: '#1e40af',
              fontWeight: 'bold',
              fontSize: '18px'
            }}
            initial={{ scale: 0, rotate: 180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ 
              delay: idx * 0.08,
              type: "spring",
              stiffness: 150,
              damping: 12
            }}
          >
            {letter}
          </motion.div>
        ))}
      </div>
      
      {decrypted && (
        <motion.div
          style={{
            marginTop: '20px',
            padding: '15px',
            backgroundColor: '#dcfce7',
            border: '2px solid #16a34a',
            borderRadius: '8px',
            textAlign: 'center'
          }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 2.5 }}
        >
          <strong style={{ color: '#15803d' }}>Decrypted Message: {decrypted}</strong>
        </motion.div>
      )}
    </div>
  );
}

// Main Spiral Cipher Tool Component
function SpiralCipherTool() {
  const [message, setMessage] = useState('MEETATNOON');
  const [mode, setMode] = useState('encrypt');

  const getEncryptedResult = () => {
    if (mode !== 'encrypt') return '';

    const cleaned = message.toUpperCase().replace(/[^A-Z]/g, '').padEnd(25, 'X').slice(0, 25);

    // Create 2D grid and fill in spiral pattern
    let spiralGrid = Array.from({ length: 5 }, () => Array(5).fill(''));
    let dx = [0, 1, 0, -1]; // right, down, left, up
    let dy = [1, 0, -1, 0];
    let dir = 0;
    let x = 0, y = 0;

    // Fill grid in spiral pattern
    for (let i = 0; i < 25; i++) {
      spiralGrid[x][y] = cleaned[i];

      let nx = x + dx[dir];
      let ny = y + dy[dir];

      if (nx < 0 || ny < 0 || nx >= 5 || ny >= 5 || spiralGrid[nx][ny] !== '') {
        dir = (dir + 1) % 4;
        nx = x + dx[dir];
        ny = y + dy[dir];
      }

      x = nx;
      y = ny;
    }

    // Read column by column to get encrypted result
    let result = '';
    for (let col = 0; col < 5; col++) {
      for (let row = 0; row < 5; row++) {
        result += spiralGrid[row][col];
      }
    }

    return result.replace(/X+$/, '');
  };

  return (
    <div style={{
      backgroundColor: '#ffffff',
      border: '2px solid #e5e7eb',
      borderRadius: '16px',
      padding: '30px',
      boxShadow: '0 10px 25px rgba(0,0,0,0.1)'
    }}>
      <h2 style={{
        fontSize: '28px',
        fontWeight: 'bold',
        color: '#374151',
        marginBottom: '20px',
        textAlign: 'center'
      }}>
        🔄 Spiral Cipher Visualizer
      </h2>
      
      {/* Input Section */}
      <div style={{ marginBottom: '20px' }}>
        <label style={{ 
          display: 'block', 
          marginBottom: '8px', 
          fontWeight: 'bold',
          color: '#374151'
        }}>
          Enter your message:
        </label>
        <input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Enter your message here..."
          style={{
            width: '100%',
            padding: '12px 16px',
            border: '2px solid #d1d5db',
            borderRadius: '8px',
            fontSize: '16px',
            outline: 'none',
            transition: 'border-color 0.2s',
          }}
          onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
          onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
        />
      </div>
      
      {/* Mode Selection */}
      <div style={{ 
        display: 'flex', 
        gap: '12px', 
        marginBottom: '30px',
        justifyContent: 'center'
      }}>
        <button
          onClick={() => setMode('encrypt')}
          style={{
            padding: '12px 24px',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            fontSize: '16px',
            fontWeight: 'bold',
            transition: 'all 0.2s',
            backgroundColor: mode === 'encrypt' ? '#f59e0b' : '#fef3c7',
            color: mode === 'encrypt' ? 'white' : '#92400e',
            transform: mode === 'encrypt' ? 'scale(1.05)' : 'scale(1)'
          }}
        >
          🕷️ Encrypt
        </button>
        <button
          onClick={() => setMode('decrypt')}
          style={{
            padding: '12px 24px',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            fontSize: '16px',
            fontWeight: 'bold',
            transition: 'all 0.2s',
            backgroundColor: mode === 'decrypt' ? '#3b82f6' : '#dbeafe',
            color: mode === 'decrypt' ? 'white' : '#1e40af',
            transform: mode === 'decrypt' ? 'scale(1.05)' : 'scale(1)'
          }}
        >
          🔓 Decrypt
        </button>
      </div>
      
      {/* Visualization */}
      {mode === 'encrypt' ? (
        <div>
          <h3 style={{ textAlign: 'center', color: '#92400e', marginBottom: '10px' }}>
            Encryption: Spiral Pattern
          </h3>
          <EncryptionGrid message={message} />
          {message && (
            <div style={{
              marginTop: '20px',
              padding: '15px',
              backgroundColor: '#fef3c7',
              border: '2px solid #f59e0b',
              borderRadius: '8px',
              textAlign: 'center'
            }}>
              <strong style={{ color: '#92400e' }}>
                Encrypted: {getEncryptedResult()}
              </strong>
            </div>
          )}
        </div>
      ) : (
        <div>
          <h3 style={{ textAlign: 'center', color: '#1e40af', marginBottom: '10px' }}>
            Decryption: Reverse Spiral
          </h3>
          <DecryptionGrid message={message} />
        </div>
      )}
    </div>
  );
}

export default SpiralCipherTool;
