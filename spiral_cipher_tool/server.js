const express = require('express');
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files from the dist directory (built React app)
app.use(express.static(path.join(__dirname, 'dist')));

// API routes (if you want to add any backend functionality later)
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Spiral Cipher Tool API is running' });
});

// Serve the React app for all other routes
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// Start the server
app.listen(PORT, () => {
  console.log(`🚀 Spiral Cipher Tool server is running on http://localhost:${PORT}`);
  console.log(`📁 Serving static files from: ${path.join(__dirname, 'dist')}`);
});

module.exports = app;
